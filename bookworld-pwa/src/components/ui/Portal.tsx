'use client';

import { useState } from 'react';
import type { ReactNode } from 'react';

interface PortalProps {
  title: ReactNode;
  description: string;
  color: 'purple' | 'rose' | 'blue' | 'amber';
  icon: ReactNode;
  onClick: () => void;
}

export default function Portal({ title, description, color, icon, onClick }: PortalProps) {
  const [isHovering, setIsHovering] = useState(false);
  const [isClicked, setIsClicked] = useState(false);

  // Color configurations
  const colorConfig = {
    purple: {
      outer: 'from-purple-600 to-indigo-800',
      inner: 'from-purple-400 to-indigo-600',
      glow: 'rgba(168,85,247,0.8)', // Brighter glow
      iconBg: 'bg-purple-500/20',
      iconBorder: 'border-purple-500/30'
    },
    rose: {
      outer: 'from-rose-600 to-orange-600',
      inner: 'from-rose-400 to-orange-500',
      glow: 'rgba(244,63,94,0.8)', // Brighter glow
      iconBg: 'bg-rose-500/20',
      iconBorder: 'border-rose-500/30'
    },
    blue: {
      outer: 'from-blue-600 to-cyan-800',
      inner: 'from-blue-400 to-cyan-600',
      glow: 'rgba(59,130,246,0.8)', // Brighter glow
      iconBg: 'bg-blue-500/20',
      iconBorder: 'border-blue-500/30'
    },
    amber: {
      outer: 'from-amber-600 to-red-700',
      inner: 'from-amber-400 to-red-500',
      glow: 'rgba(245,158,11,0.8)', // Brighter glow
      iconBg: 'bg-amber-500/20',
      iconBorder: 'border-amber-500/30'
    }
  };

  const selectedColor = colorConfig[color];

  const handleClick = () => {
    setIsClicked(true);

    // Delay navigation to allow animation to play
    setTimeout(() => {
      onClick();
    }, 600);
  };

  return (
    <div
      className="relative"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      {/* Portal outer ring - animated on hover */}
      <div
        className={`
          w-52 h-52 rounded-full
          bg-gradient-to-br ${selectedColor.outer}
          transition-all duration-500 ease-out
          ${isHovering ? 'scale-110 opacity-90' : 'scale-100 opacity-70'}
          ${isClicked ? 'scale-[30] opacity-100' : ''}
        `}
      />

      {/* Portal inner circle with swirling effect */}
      <div
        className={`
          absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
          w-44 h-44 rounded-full overflow-hidden
          transition-all duration-500
          ${isHovering ? 'scale-110' : 'scale-100'}
          ${isClicked ? 'scale-0' : ''}
        `}
      >
        <div className="absolute inset-0 bg-black/80" />

        {/* Swirling effect */}
        <div
          className={`
            absolute inset-0
            bg-gradient-to-br ${selectedColor.inner}
            opacity-70
            animate-portal-swirl
          `}
        />

        {/* Radial gradient overlay */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,transparent_30%,rgba(0,0,0,0.8)_100%)]" />
      </div>

      {/* Content */}
      <div
        className={`
          absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
          flex flex-col items-center justify-center
          transition-all duration-500
          ${isClicked ? 'opacity-0 scale-0' : 'opacity-100'}
          ${isHovering ? 'scale-110' : 'scale-100'}
          w-full h-full
        `}
      >
        {icon && (
          <div className={`
            w-20 h-20 rounded-full
            ${selectedColor.iconBg}
            backdrop-blur-sm
            border ${selectedColor.iconBorder}
            flex items-center justify-center
            mb-3
            text-white
          `}>
            {icon}
          </div>
        )}

        {/* Text */}
        <div className="text-white font-bold text-2xl text-center tracking-wide max-w-[80%] leading-tight flex flex-col items-center justify-center space-y-1">
          {title}
        </div>
        {description && (
          <p className="text-white/90 text-base text-center max-w-[140px] leading-snug mt-2">{description}</p>
        )}
      </div>

      {/* Glow effect on hover */}
      <div
        className={`
          absolute inset-0 rounded-full
          transition-all duration-500 ease-out
          ${isHovering ? 'opacity-100 blur-xl' : 'opacity-0 blur-md'}
          ${isClicked ? 'opacity-100 scale-[30] blur-xl' : ''}
        `}
        style={{
          background: `radial-gradient(circle at center, ${selectedColor.glow} 0%, transparent 70%)`,
        }}
      />

      {/* Interactive overlay */}
      <button
        type="button"
        onClick={handleClick}
        className="absolute inset-0 rounded-full cursor-pointer z-10"
        aria-label={`Go to ${title}`}
      />
    </div>
  );
}
