import React, { HTMLAttributes } from 'react';

interface CardProps extends HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  variant?: 'default' | 'outline' | 'ghost';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  border?: boolean;
  fullWidth?: boolean;
}

export default function Card({
  children,
  variant = 'default',
  padding = 'md',
  rounded = 'md',
  shadow = 'sm',
  border = true,
  fullWidth = false,
  className = '',
  ...props
}: CardProps) {
  const variantStyles = {
    default: 'bg-white',
    outline: 'bg-transparent',
    ghost: 'bg-gray-50',
  };
  
  const paddingStyles = {
    none: 'p-0',
    sm: 'p-3',
    md: 'p-5',
    lg: 'p-8',
  };
  
  const roundedStyles = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    full: 'rounded-full',
  };
  
  const shadowStyles = {
    none: 'shadow-none',
    sm: 'shadow-sm',
    md: 'shadow',
    lg: 'shadow-lg',
  };
  
  const borderStyles = border ? 'border border-gray-200' : '';
  const widthStyles = fullWidth ? 'w-full' : '';
  
  const combinedClassName = `
    ${variantStyles[variant]}
    ${paddingStyles[padding]}
    ${roundedStyles[rounded]}
    ${shadowStyles[shadow]}
    ${borderStyles}
    ${widthStyles}
    ${className}
  `.trim();
  
  return (
    <div className={combinedClassName} {...props}>
      {children}
    </div>
  );
}
