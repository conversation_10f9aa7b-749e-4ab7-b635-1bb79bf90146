import { useRef, useEffect } from 'react';
import * as THREE from 'three';

export default function BookGlobe() {
  const containerRef = useRef(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();

    // Responsive sizing
    const container = containerRef.current;
    const width = container.clientWidth;
    const height = container.clientHeight;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
    camera.position.z = 5;

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true // Transparent background
    });
    renderer.setSize(width, height);
    renderer.setPixelRatio(window.devicePixelRatio);
    container.appendChild(renderer.domElement);

    // Create the globe sphere
    const sphereGeometry = new THREE.SphereGeometry(1.5, 64, 64);

    // Create a special material for the globe
    const material = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        resolution: { value: new THREE.Vector2(width, height) }
      },
      vertexShader: `
        varying vec2 vUv;
        varying vec3 vPosition;

        void main() {
          vUv = uv;
          vPosition = position;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec2 resolution;
        varying vec2 vUv;
        varying vec3 vPosition;

        void main() {
          // Create a grid pattern
          float gridSize = 20.0;
          vec2 grid = fract(vUv * gridSize);

          // Create thinner lines for the grid
          float lineWidth = 0.02; // Thinner lines
          float horizontalLine = smoothstep(0.0, lineWidth, grid.x) * smoothstep(lineWidth, 0.0, grid.x);
          float verticalLine = smoothstep(0.0, lineWidth, grid.y) * smoothstep(lineWidth, 0.0, grid.y);

          // Combine lines
          float gridPattern = max(horizontalLine, verticalLine);

          // Enhanced color palette for a more vibrant neon look
          vec3 deepPurple = vec3(0.5, 0.0, 1.0);  // Brighter deep purple base
          vec3 neonPink = vec3(1.0, 0.3, 0.9);    // More vibrant neon pink
          vec3 electricBlue = vec3(0.3, 0.7, 1.0); // Brighter electric blue accent

          // Create a more complex animated glow effect
          float timeScale = time * 0.3;
          float positionFactor = vPosition.x * 1.5 + vPosition.y * 1.5 + vPosition.z * 1.5;
          float glow1 = sin(timeScale + positionFactor) * 0.5 + 0.5;
          float glow2 = cos(timeScale * 0.7 + positionFactor * 1.3) * 0.5 + 0.5;

          // Mix colors based on the animated glow values
          vec3 color1 = mix(deepPurple, neonPink, glow1);
          vec3 color2 = mix(color1, electricBlue, glow2 * 0.4); // Increased color mixing

          // Add pulsing intensity to the grid lines
          float pulseIntensity = 1.0 + sin(time * 0.5) * 0.3; // Increased intensity

          // Combine grid and glow with enhanced intensity
          vec3 finalColor = color2 * (gridPattern * pulseIntensity + 0.4); // Increased base brightness

          // Add bloom effect to the grid lines
          float bloomFactor = gridPattern * 0.7; // Increased bloom
          finalColor += bloomFactor * neonPink * 0.8; // Increased bloom intensity

          // Adjust alpha for a subtle glow between grid lines
          float alpha = gridPattern * 0.9 + 0.15; // Increased overall opacity

          gl_FragColor = vec4(finalColor, alpha);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide
    });

    const globe = new THREE.Mesh(sphereGeometry, material);
    scene.add(globe);

    // Add a subtle ambient light
    const ambientLight = new THREE.AmbientLight(0x404040);
    scene.add(ambientLight);

    // Add a directional light
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 3, 5);
    scene.add(directionalLight);

    // Add enhanced stars in the background with color variation
    const starsGeometry = new THREE.BufferGeometry();

    // Create stars with color variation
    const starsVertices = [];
    const starsColors = [];

    // Color palette for stars
    const starColors = [
      new THREE.Color(0xffffff), // White
      new THREE.Color(0xffccff), // Light pink
      new THREE.Color(0xccccff), // Light blue
      new THREE.Color(0xff88ff), // Pink
      new THREE.Color(0xaa88ff)  // Purple
    ];

    for (let i = 0; i < 1500; i++) { // More stars
      const x = (Math.random() - 0.5) * 12; // Wider distribution
      const y = (Math.random() - 0.5) * 12;
      const z = (Math.random() - 0.5) * 12;

      // Ensure stars are outside the globe
      const distance = Math.sqrt(x*x + y*y + z*z);
      if (distance > 2.0) {
        starsVertices.push(x, y, z);

        // Randomly select a color from the palette
        const color = starColors[Math.floor(Math.random() * starColors.length)];
        starsColors.push(color.r, color.g, color.b);
      }
    }

    starsGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starsVertices, 3));
    starsGeometry.setAttribute('color', new THREE.Float32BufferAttribute(starsColors, 3));

    const starsMaterial = new THREE.PointsMaterial({
      size: 0.03, // Slightly larger stars
      transparent: true,
      opacity: 0.8,
      vertexColors: true, // Use the colors we defined
      blending: THREE.AdditiveBlending // Add glow effect to stars
    });
    const stars = new THREE.Points(starsGeometry, starsMaterial);
    scene.add(stars);

    // Animation loop
    const clock = new THREE.Clock();

    const animate = () => {
      requestAnimationFrame(animate);

      const elapsedTime = clock.getElapsedTime();
      material.uniforms.time.value = elapsedTime;

      // Enhanced globe rotation with subtle movements
      globe.rotation.y = elapsedTime * 0.1;
      globe.rotation.x = Math.sin(elapsedTime * 0.05) * 0.1;
      globe.rotation.z = Math.sin(elapsedTime * 0.03) * 0.02; // Slight wobble

      // Subtle scale pulsing effect
      const scalePulse = 1.0 + Math.sin(elapsedTime * 0.2) * 0.01;
      globe.scale.set(scalePulse, scalePulse, scalePulse);

      // Animate the stars with a more complex motion
      stars.rotation.y = elapsedTime * 0.02;
      stars.rotation.x = Math.sin(elapsedTime * 0.01) * 0.01;

      renderer.render(scene, camera);
    };

    animate();

    // Handle window resize
    const handleResize = () => {
      const width = container.clientWidth;
      const height = container.clientHeight;

      camera.aspect = width / height;
      camera.updateProjectionMatrix();

      renderer.setSize(width, height);
      material.uniforms.resolution.value.set(width, height);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      container.removeChild(renderer.domElement);
      scene.remove(globe);
      sphereGeometry.dispose();
      material.dispose();
      starsGeometry.dispose();
      starsMaterial.dispose();
    };
  }, []);

  return (
    <div ref={containerRef} className="w-full h-full" />
  );
}
