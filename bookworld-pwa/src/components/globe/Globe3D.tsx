'use client';

import { useEffect, useRef } from 'react';
import * as THREE from 'three';

interface Globe3DProps {
  width?: number;
  height?: number;
  className?: string;
}

export default function Globe3D({ width = 500, height = 500, className = '' }: Globe3DProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const globeRef = useRef<THREE.Mesh | null>(null);
  const frameIdRef = useRef<number>(0);

  useEffect(() => {
    if (!containerRef.current) return;

    // Initialize scene
    const scene = new THREE.Scene();
    sceneRef.current = scene;

    // Initialize camera
    const camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
    camera.position.z = 5;
    cameraRef.current = camera;

    // Initialize renderer
    const renderer = new THREE.WebGLRenderer({ 
      antialias: true,
      alpha: true // Transparent background
    });
    renderer.setSize(width, height);
    renderer.setPixelRatio(window.devicePixelRatio);
    containerRef.current.innerHTML = '';
    containerRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Add lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 5, 5);
    scene.add(directionalLight);

    // Create globe geometry
    const geometry = new THREE.SphereGeometry(2, 64, 64);

    // Create materials
    const landMaterial = new THREE.MeshPhongMaterial({
      color: 0x111111, // Black onyx color
      shininess: 100,
      specular: 0x333333,
    });

    const waterMaterial = new THREE.MeshPhongMaterial({
      color: 0x222222, // Dark gray for water
      shininess: 150,
      specular: 0xffffff, // Diamond-like sparkle
      opacity: 0.9,
      transparent: true,
    });

    // Create a texture loader
    const textureLoader = new THREE.TextureLoader();

    // Load a world map texture (land/water mask)
    textureLoader.load('/images/world-map-mask.png', (texture) => {
      // Create a custom shader material that uses the mask texture
      const customMaterial = new THREE.ShaderMaterial({
        uniforms: {
          landTexture: { value: landMaterial.map },
          waterTexture: { value: waterMaterial.map },
          maskTexture: { value: texture },
          time: { value: 0.0 }
        },
        vertexShader: `
          varying vec2 vUv;
          void main() {
            vUv = uv;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
          }
        `,
        fragmentShader: `
          uniform sampler2D maskTexture;
          uniform float time;
          varying vec2 vUv;
          
          void main() {
            // Sample the mask texture
            vec4 maskColor = texture2D(maskTexture, vUv);
            
            // Land is white in the mask, water is black
            float landFactor = maskColor.r;
            
            // Create a base color for land (black onyx)
            vec3 landColor = vec3(0.05, 0.05, 0.05);
            
            // Create a base color for water with some shimmer effect
            float shimmer = sin(vUv.x * 20.0 + time) * sin(vUv.y * 20.0 + time) * 0.05;
            vec3 waterColor = vec3(0.1 + shimmer, 0.1 + shimmer, 0.1 + shimmer);
            
            // Add some specular highlights to water to simulate diamond effect
            float specular = pow(max(0.0, sin(vUv.x * 30.0 + time * 2.0) * sin(vUv.y * 30.0 + time)), 20.0) * 0.5;
            waterColor += vec3(specular);
            
            // Mix the colors based on the mask
            vec3 finalColor = mix(waterColor, landColor, landFactor);
            
            gl_FragColor = vec4(finalColor, 1.0);
          }
        `
      });

      // Create the globe with the custom material
      const globe = new THREE.Mesh(geometry, customMaterial);
      scene.add(globe);
      globeRef.current = globe;

      // Animation loop
      const animate = () => {
        if (!globeRef.current || !rendererRef.current || !sceneRef.current || !cameraRef.current) return;
        
        // Rotate the globe
        globeRef.current.rotation.y += 0.002;
        
        // Update the time uniform for the shader
        if (customMaterial.uniforms) {
          customMaterial.uniforms.time.value += 0.01;
        }
        
        // Render the scene
        rendererRef.current.render(sceneRef.current, cameraRef.current);
        
        // Request the next frame
        frameIdRef.current = requestAnimationFrame(animate);
      };
      
      animate();
    }, 
    undefined, // onProgress callback
    () => {
      // If the texture fails to load, create a basic globe
      const globe = new THREE.Mesh(geometry, landMaterial);
      scene.add(globe);
      globeRef.current = globe;
      
      // Animation loop for basic globe
      const animate = () => {
        if (!globeRef.current || !rendererRef.current || !sceneRef.current || !cameraRef.current) return;
        
        // Rotate the globe
        globeRef.current.rotation.y += 0.002;
        
        // Render the scene
        rendererRef.current.render(sceneRef.current, cameraRef.current);
        
        // Request the next frame
        frameIdRef.current = requestAnimationFrame(animate);
      };
      
      animate();
    });

    // Handle window resize
    const handleResize = () => {
      if (!cameraRef.current || !rendererRef.current || !containerRef.current) return;
      
      const newWidth = containerRef.current.clientWidth;
      const newHeight = containerRef.current.clientHeight;
      
      cameraRef.current.aspect = newWidth / newHeight;
      cameraRef.current.updateProjectionMatrix();
      
      rendererRef.current.setSize(newWidth, newHeight);
    };
    
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => {
      window.removeEventListener('resize', handleResize);
      cancelAnimationFrame(frameIdRef.current);
      
      if (rendererRef.current && rendererRef.current.domElement && containerRef.current) {
        containerRef.current.removeChild(rendererRef.current.domElement);
      }
      
      if (globeRef.current) {
        geometry.dispose();
        if (globeRef.current.material instanceof THREE.Material) {
          globeRef.current.material.dispose();
        } else if (Array.isArray(globeRef.current.material)) {
          globeRef.current.material.forEach(material => material.dispose());
        }
      }
      
      if (rendererRef.current) {
        rendererRef.current.dispose();
      }
    };
  }, [width, height]);

  return (
    <div 
      ref={containerRef} 
      className={`relative ${className}`} 
      style={{ width, height }}
    />
  );
}
