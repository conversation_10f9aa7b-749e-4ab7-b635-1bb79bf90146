'use client';

import { useState } from 'react';
import Image from 'next/image';
import Button from '@/components/ui/Button';
import ProfilePicture from '@/components/profile/ProfilePicture';
import ProfilePictureSelector from '@/components/profile/ProfilePictureSelector';
import { BookData } from '@/components/books/BookCard';
import { AuthorProfile } from '@/lib/utils/author';

interface SocialLinks {
  [key: string]: string;
}

interface Author {
  id: string;
  name: string;
  photoUrl: string;
  bio: string;
  website: string;
  socialLinks: SocialLinks;
}

interface EditProfileFormProps {
  author: AuthorProfile;
  userBooks: BookData[];
  onSave: (updatedAuthor: AuthorProfile) => void;
  onCancel: () => void;
}

export default function EditProfileForm({ 
  author, 
  userBooks, 
  onSave, 
  onCancel 
}: EditProfileFormProps) {
  const [formData, setFormData] = useState<AuthorProfile>(author);
  const [isEditingProfilePic, setIsEditingProfilePic] = useState(false);
  const [socialPlatform, setSocialPlatform] = useState('');
  const [socialUrl, setSocialUrl] = useState('');
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleProfilePictureChange = async (imageFile: File | string) => {
    if (typeof imageFile === 'string') {
      setFormData(prev => ({ ...prev, photoUrl: imageFile }));
    } else {
      // Handle File upload if needed
      // For now, we'll just use the string URL
      console.warn('File upload not implemented yet');
    }
  };
  
  const handleAddSocialLink = () => {
    if (!socialPlatform || !socialUrl) return;
    
    setFormData(prev => ({
      ...prev,
      socialLinks: {
        ...prev.socialLinks,
        [socialPlatform.toLowerCase()]: socialUrl
      }
    }));
    
    setSocialPlatform('');
    setSocialUrl('');
  };
  
  const handleRemoveSocialLink = (platform: string) => {
    const updatedSocialLinks = {...formData.socialLinks};
    delete updatedSocialLinks[platform];
    
    setFormData(prev => ({
      ...prev,
      socialLinks: updatedSocialLinks
    }));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };
  
  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-2xl font-bold mb-6">Edit Profile</h2>
      
      {isEditingProfilePic ? (
        <div className="mb-8">
          <h3 className="text-lg font-medium mb-4">Choose Profile Picture</h3>
          <ProfilePictureSelector
            currentImageUrl={formData.photoUrl}
            userBooks={userBooks}
            onSave={handleProfilePictureChange}
          />
        </div>
      ) : (
        <div className="flex items-center mb-8">
          <div className="relative">
            <ProfilePicture
              src={formData.photoUrl}
              alt={formData.name}
              size="lg"
            />
            <button
              type="button"
              className="absolute -bottom-2 -right-2 bg-white rounded-full p-1 shadow-lg hover:scale-110 transition-transform"
              onClick={() => setIsEditingProfilePic(true)}
              aria-label="Edit profile picture"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
            </button>
          </div>
          <div className="ml-4">
            <p className="text-sm text-gray-500">Click the edit icon to change your profile picture</p>
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          {/* Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
              required
            />
          </div>
          
          {/* Bio */}
          <div>
            <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
              Bio
            </label>
            <textarea
              id="bio"
              name="bio"
              value={formData.bio}
              onChange={handleInputChange}
              rows={4}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
            />
          </div>
          
          {/* Website */}
          <div>
            <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1">
              Website
            </label>
            <input
              type="url"
              id="website"
              name="website"
              value={formData.website}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
              placeholder="https://yourwebsite.com"
            />
          </div>
          
          {/* Social Links */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Social Media Links
            </label>
            
            <div className="space-y-3 mb-4">
              {Object.entries(formData.socialLinks).map(([platform, url]) => (
                <div key={platform} className="flex items-center">
                  <div className="flex-1 flex items-center">
                    <span className="font-medium capitalize mr-2">{platform}:</span>
                    <span className="text-gray-600 text-sm truncate">{url}</span>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleRemoveSocialLink(platform)}
                    className="text-red-500 hover:text-red-700"
                    aria-label={`Remove ${platform}`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              ))}
            </div>
            
            <div className="flex space-x-2">
              <select
                value={socialPlatform}
                onChange={(e) => setSocialPlatform(e.target.value)}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
              >
                <option value="">Select Platform</option>
                <option value="twitter">Twitter</option>
                <option value="instagram">Instagram</option>
                <option value="facebook">Facebook</option>
                <option value="tiktok">TikTok</option>
                <option value="youtube">YouTube</option>
              </select>
              <input
                type="url"
                value={socialUrl}
                onChange={(e) => setSocialUrl(e.target.value)}
                placeholder="https://..."
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
              />
              <button
                type="button"
                onClick={handleAddSocialLink}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
                disabled={!socialPlatform || !socialUrl}
              >
                Add
              </button>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button
              type="submit"
            >
              Save Changes
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
