'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface ProfilePictureProps {
  src?: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  usageCount?: number;
  showUsageCount?: boolean;
}

export default function ProfilePicture({
  src,
  alt,
  size = 'md',
  className = '',
  usageCount = 0,
  showUsageCount = false
}: ProfilePictureProps) {
  const [error, setError] = useState(false);

  // Reset error state if src changes
  useEffect(() => {
    setError(false);
  }, [src]);

  // Size mapping (width in pixels, height will be calculated based on 10:16 ratio)
  const sizeMap = {
    sm: 60,
    md: 100,
    lg: 160,
    xl: 240
  };

  const width = sizeMap[size];
  const height = Math.round(width * 1.6); // 10:16 aspect ratio (1.6 = 16/10)

  // Default placeholder if no image or error
  const placeholderSrc = '/images/profile-placeholder.svg';

  return (
    <div className="relative">
      {/* Book cover with spine effect */}
      <div
        className={`relative overflow-hidden rounded-md ${className}`}
        style={{
          width: `${width}px`,
          height: `${height}px`,
          // Book-like shadow with spine effect
          boxShadow: `
            0 4px 6px rgba(0, 0, 0, 0.1),
            0 1px 3px rgba(0, 0, 0, 0.08),
            -5px 0 10px -5px rgba(0, 0, 0, 0.3) inset
          `,
          background: 'linear-gradient(to right, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 5%)'
        }}
      >
        <Image
          src={error || !src ? placeholderSrc : src}
          alt={alt}
          fill
          sizes={`${width}px`}
          className="object-cover"
          onError={() => setError(true)}
        />
      </div>

      {/* Usage count badge */}
      {showUsageCount && usageCount > 0 && (
        <div
          className="absolute -bottom-2 -right-2 bg-purple-600 text-white text-xs font-bold rounded-full flex items-center justify-center"
          style={{
            width: `${Math.max(width * 0.25, 24)}px`,
            height: `${Math.max(width * 0.25, 24)}px`,
            boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
          }}
        >
          {usageCount}
        </div>
      )}
    </div>
  );
}
