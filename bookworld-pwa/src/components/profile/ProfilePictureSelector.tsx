'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import ProfilePicture from './ProfilePicture';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useRouter } from 'next/navigation';

interface BookCover {
  id: string;
  title: string;
  coverUrl: string;
  author?: {
    id: string;
    name: string;
  };
  releaseDate?: Date;
  usageCount?: number;
}

interface Author {
  id: string;
  name: string;
  photoUrl?: string;
  comingSoonBooks: BookCover[];
}

interface ProfilePictureSelectorProps {
  currentImageUrl?: string;
  userBooks?: BookCover[];
  onSave: (imageFile: File | string) => Promise<void>;
  hideSaveButton?: boolean;
  onCancel?: () => void;
}

export default function ProfilePictureSelector({
  currentImageUrl,
  userBooks = [],
  onSave,
  hideSaveButton,
  onCancel
}: ProfilePictureSelectorProps) {
  const [activeTab, setActiveTab] = useState<'upload' | 'myBooks' | 'allBooks' | 'comingSoon' | 'authors'>('upload');
  const [selectedImage, setSelectedImage] = useState<string | null>(currentImageUrl || null);
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [cropSettings, setCropSettings] = useState({ x: 0, y: 0, scale: 1 });
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [allBooks, setAllBooks] = useState<BookCover[]>([]);
  const [comingSoonBooks, setComingSoonBooks] = useState<BookCover[]>([]);
  const [authors, setAuthors] = useState<Author[]>([]);
  const [selectedAuthor, setSelectedAuthor] = useState<Author | null>(null);
  const [isLoadingBooks, setIsLoadingBooks] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Fetch coming soon books on component mount
  useEffect(() => {
    fetchComingSoonBooks();
    fetchAuthors();
  }, []);

  // Handle book selection
  const handleBookSelect = (book: BookCover) => {
    console.log('Book selected:', book);
    
    // Set the selected image immediately
    setSelectedImage(book.coverUrl);
    
    // If hideSaveButton is true, auto-save after a short delay
    // to ensure the state has been updated
    if (hideSaveButton) {
      console.log('Auto-saving selected book cover...');
      // Use a longer delay to ensure state has been updated
      setTimeout(() => {
        console.log('Selected image state:', selectedImage);
        console.log('Book cover URL:', book.coverUrl);
        
        // Call onSave directly with the book cover URL instead of using handleSave
        // This avoids the state check in handleSave that might fail due to React's state batching
        if (book.coverUrl) {
          console.log('Calling onSave with book cover URL:', book.coverUrl);
          onSave(book.coverUrl);
        } else {
          console.error('Book cover URL is missing');
          setError('Selected book cover URL is missing. Please try again.');
        }
      }, 1000); // Increased delay to 1000ms
    }
  };

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      alert('Please upload an image file');
      return;
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size should be less than 5MB');
      return;
    }

    console.log('File selected:', file.name, file.type, file.size, 'bytes');
    setUploadedImage(file);

    // Create preview URL
    const reader = new FileReader();
    reader.onload = () => {
      console.log('Preview URL created');
      setPreviewUrl(reader.result as string);
      
      // If hideSaveButton is true, automatically save the uploaded image
      if (hideSaveButton) {
        console.log('Auto-saving uploaded image due to hideSaveButton=true');
        // We need to wait longer for the state to update
        setTimeout(() => {
          console.log('Checking state before auto-saving:');
          console.log('uploadedImage:', uploadedImage);
          console.log('previewUrl:', previewUrl);
          
          // Call onSave directly with the file instead of using handleSave
          // This avoids the state check in handleSave that might fail due to React's state batching
          console.log('Calling onSave with file:', file.name);
          onSave(file);
        }, 1000); // Increased delay to 1000ms
      }
    };
    reader.readAsDataURL(file);

    // Reset crop settings
    setCropSettings({ x: 0, y: 0, scale: 1 });
  };

  // Fetch coming soon books
  const fetchComingSoonBooks = async () => {
    setIsLoadingBooks(true);
    console.log('Fetching coming soon books...');

    try {
      // This would be replaced with an actual API call
      // For now, we'll simulate a delay and return mock data
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data for demonstration
      setComingSoonBooks([
        {
          id: '1',
          title: "The Dragon's Legacy",
          coverUrl: 'https://via.placeholder.com/160x256/3949AB/FFFFFF?text=Coming+Soon',
          author: { id: 'a1', name: 'Emily Chen' },
          releaseDate: new Date(Date.now() + 86400000 * 30), // 30 days from now
          usageCount: 42
        },
        {
          id: '2',
          title: 'Whispers in the Dark',
          coverUrl: 'https://via.placeholder.com/160x256/D81B60/FFFFFF?text=Coming+Soon',
          author: { id: 'a2', name: 'James Wilson' },
          releaseDate: new Date(Date.now() + 86400000 * 15), // 15 days from now
          usageCount: 28
        },
        {
          id: '3',
          title: 'Chronicles of the Forgotten',
          coverUrl: 'https://via.placeholder.com/160x256/388E3C/FFFFFF?text=Coming+Soon',
          author: { id: 'a3', name: 'Sarah Johnson' },
          releaseDate: new Date(Date.now() + 86400000 * 45), // 45 days from now
          usageCount: 15
        },
        {
          id: '4',
          title: 'The Last Frontier',
          coverUrl: 'https://via.placeholder.com/160x256/FFA000/FFFFFF?text=Coming+Soon',
          author: { id: 'a1', name: 'Emily Chen' },
          releaseDate: new Date(Date.now() + 86400000 * 60), // 60 days from now
          usageCount: 7
        },
      ]);
      console.log('Coming soon books loaded');
    } catch (error) {
      console.error('Error fetching coming soon books:', error);
    } finally {
      setIsLoadingBooks(false);
    }
  };

  // Fetch authors with coming soon books
  const fetchAuthors = async () => {
    console.log('Fetching authors...');
    try {
      // This would be replaced with an actual API call
      // For now, we'll use mock data
      setAuthors([
        {
          id: 'a1',
          name: 'Emily Chen',
          photoUrl: 'https://via.placeholder.com/100x100/3949AB/FFFFFF?text=EC',
          comingSoonBooks: [
            {
              id: '1',
              title: "The Dragon's Legacy",
              coverUrl: 'https://via.placeholder.com/160x256/3949AB/FFFFFF?text=Coming+Soon',
              releaseDate: new Date(Date.now() + 86400000 * 30),
              usageCount: 42
            },
            {
              id: '4',
              title: 'The Last Frontier',
              coverUrl: 'https://via.placeholder.com/160x256/FFA000/FFFFFF?text=Coming+Soon',
              releaseDate: new Date(Date.now() + 86400000 * 60),
              usageCount: 7
            }
          ]
        },
        {
          id: 'a2',
          name: 'James Wilson',
          photoUrl: 'https://via.placeholder.com/100x100/D81B60/FFFFFF?text=JW',
          comingSoonBooks: [
            {
              id: '2',
              title: 'Whispers in the Dark',
              coverUrl: 'https://via.placeholder.com/160x256/D81B60/FFFFFF?text=Coming+Soon',
              releaseDate: new Date(Date.now() + 86400000 * 15),
              usageCount: 28
            }
          ]
        },
        {
          id: 'a3',
          name: 'Sarah Johnson',
          photoUrl: 'https://via.placeholder.com/100x100/388E3C/FFFFFF?text=SJ',
          comingSoonBooks: [
            {
              id: '3',
              title: 'Chronicles of the Forgotten',
              coverUrl: 'https://via.placeholder.com/160x256/388E3C/FFFFFF?text=Coming+Soon',
              releaseDate: new Date(Date.now() + 86400000 * 45),
              usageCount: 15
            }
          ]
        }
      ]);
      console.log('Authors loaded');
    } catch (error) {
      console.error('Error fetching authors:', error);
    }
  };

  // Handle search
  const handleSearch = async () => {
    if (searchQuery.trim() === '') return;

    setIsLoadingBooks(true);
    console.log('Searching for books with query:', searchQuery);

    try {
      // This would be replaced with an actual API call
      // For now, we'll simulate a delay and return mock data
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data for demonstration
      setAllBooks([
        {
          id: '1',
          title: 'The Great Adventure',
          coverUrl: 'https://via.placeholder.com/160x256/3949AB/FFFFFF?text=Book+1',
          author: { id: 'a1', name: 'Emily Chen' },
          usageCount: 24
        },
        {
          id: '2',
          title: 'Mystery of the Lost Key',
          coverUrl: 'https://via.placeholder.com/160x256/D81B60/FFFFFF?text=Book+2',
          author: { id: 'a2', name: 'James Wilson' },
          usageCount: 18
        },
        {
          id: '3',
          title: 'Beyond the Horizon',
          coverUrl: 'https://via.placeholder.com/160x256/388E3C/FFFFFF?text=Book+3',
          author: { id: 'a3', name: 'Sarah Johnson' },
          usageCount: 32
        },
        {
          id: '4',
          title: 'Secrets of the Ancient Temple',
          coverUrl: 'https://via.placeholder.com/160x256/FFA000/FFFFFF?text=Book+4',
          author: { id: 'a1', name: 'Emily Chen' },
          usageCount: 15
        },
      ]);
      console.log('Search results loaded');
    } catch (error) {
      console.error('Error searching books:', error);
      alert('Failed to search books. Please try again.');
    } finally {
      setIsLoadingBooks(false);
    }
  };

  // Handle author selection
  const handleAuthorSelect = (author: Author) => {
    console.log('Author selected:', author.name);
    setSelectedAuthor(author);
  };

  // Handle crop adjustments
  const handleCropChange = (type: 'x' | 'y' | 'scale', value: number) => {
    console.log('Crop adjustment:', type, value);
    setCropSettings(prev => ({
      ...prev,
      [type]: value
    }));
  };

  // Handle save
  const handleSave = () => {
    console.log('handleSave called with:');
    console.log('selectedImage:', selectedImage);
    console.log('uploadedImage:', uploadedImage);
    console.log('previewUrl:', previewUrl);
    
    if (!selectedImage && !uploadedImage) {
      console.error('No image selected or uploaded');
      setError('Please select an image or upload a file');
      return;
    }
    
    try {
      // If we have a selected image, use that
      if (selectedImage) {
        console.log('Using selected image:', selectedImage);
        onSave(selectedImage);
        return;
      }
      
      // If we have an uploaded image, use that
      if (uploadedImage) {
        console.log('Using uploaded image');
        onSave(uploadedImage);
        return;
      }
      
      // This should never happen due to the check above, but just in case
      console.error('Unexpected state: no image available');
      setError('An unexpected error occurred. Please try again.');
    } catch (error) {
      console.error('Error in handleSave:', error);
      setError('An error occurred while saving. Please try again.');
    }
  };

  // Handle cancel
  const handleCancel = () => {
    console.log('Cancelling profile picture selection');
    if (onCancel) {
      onCancel();
    } else {
      router.back();
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Choose Your Profile Picture</h2>
      <p className="text-sm text-gray-600 mb-4">
        Your profile picture will be displayed in a 10:16 book cover format. Choose a book cover from our collection or upload your own image.
      </p>

      {/* Preview */}
      <div className="flex justify-center mb-8">
        <div className="flex flex-col items-center">
          <h3 className="text-lg font-medium mb-2 text-gray-700">Preview</h3>
          {previewUrl ? (
            <div
              className="relative overflow-hidden rounded-md"
              style={{
                width: '160px',
                height: '256px', // 10:16 aspect ratio
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)'
              }}
            >
              <Image
                src={previewUrl}
                alt="Preview"
                fill
                sizes="160px"
                className="object-cover"
                style={{
                  objectPosition: `${50 + cropSettings.x}% ${50 + cropSettings.y}%`,
                  transform: `scale(${cropSettings.scale})`
                }}
              />
            </div>
          ) : (
            <ProfilePicture
              src={selectedImage || undefined}
              alt="Profile Preview"
              size="lg"
            />
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="flex flex-wrap border-b border-gray-200 mb-6">
        <button
          type="button"
          className={`px-4 py-2 font-medium text-sm ${activeTab === 'upload' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500 hover:text-gray-700'}`}
          onClick={() => setActiveTab('upload')}
        >
          Upload Photo
        </button>
        <button
          type="button"
          className={`px-4 py-2 font-medium text-sm ${activeTab === 'myBooks' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500 hover:text-gray-700'}`}
          onClick={() => setActiveTab('myBooks')}
        >
          Your Book Covers
        </button>
        <button
          type="button"
          className={`px-4 py-2 font-medium text-sm ${activeTab === 'comingSoon' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500 hover:text-gray-700'}`}
          onClick={() => setActiveTab('comingSoon')}
        >
          Coming Soon
        </button>
        <button
          type="button"
          className={`px-4 py-2 font-medium text-sm ${activeTab === 'authors' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500 hover:text-gray-700'}`}
          onClick={() => setActiveTab('authors')}
        >
          By Author
        </button>
        <button
          type="button"
          className={`px-4 py-2 font-medium text-sm ${activeTab === 'allBooks' ? 'text-purple-600 border-b-2 border-purple-600' : 'text-gray-500 hover:text-gray-700'}`}
          onClick={() => setActiveTab('allBooks')}
        >
          Browse All
        </button>
      </div>

      {/* Tab Content */}
      <div className="mb-8">
        {/* Upload Photo Tab */}
        {activeTab === 'upload' && (
          <div>
            <div className="mb-6">
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                Select Image
              </button>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept="image/*"
                className="hidden"
              />
              <p className="mt-2 text-sm text-gray-500">
                <span className="font-medium">Important:</span> Your profile picture will be displayed in a 10:16 book cover format. 
                For best results, upload an image with this aspect ratio. Max size: 5MB.
              </p>
              <div className="mt-2 p-3 bg-gray-50 rounded-md border border-gray-200">
                <p className="text-xs text-gray-600">
                  <span className="font-medium">Tip:</span> If your image doesn't have the right aspect ratio, 
                  you can adjust its position and zoom using the controls below after uploading.
                </p>
              </div>
            </div>

            {previewUrl && (
              <div className="space-y-4">
                <h4 className="font-medium text-gray-700">Adjust Image</h4>

                <div className="space-y-2">
                  <label htmlFor="position-x" className="block text-sm text-gray-600">Position X</label>
                  <input
                    id="position-x"
                    type="range"
                    min="-50"
                    max="50"
                    value={cropSettings.x}
                    onChange={(e) => handleCropChange('x', Number.parseInt(e.target.value, 10))}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="position-y" className="block text-sm text-gray-600">Position Y</label>
                  <input
                    id="position-y"
                    type="range"
                    min="-50"
                    max="50"
                    value={cropSettings.y}
                    onChange={(e) => handleCropChange('y', Number.parseInt(e.target.value, 10))}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="zoom" className="block text-sm text-gray-600">Zoom</label>
                  <input
                    id="zoom"
                    type="range"
                    min="1"
                    max="2"
                    step="0.01"
                    value={cropSettings.scale}
                    onChange={(e) => handleCropChange('scale', Number.parseFloat(e.target.value))}
                    className="w-full"
                  />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Your Book Covers Tab */}
        {activeTab === 'myBooks' && (
          <div>
            {userBooks.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">You haven't published any books yet.</p>
                <button
                  type="button"
                  onClick={() => setActiveTab('upload')}
                  className="mt-4 text-purple-600 hover:text-purple-800 font-medium"
                >
                  Upload a photo instead
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {userBooks.map(book => (
                  <div
                    key={book.id}
                    className={`cursor-pointer transition-all duration-200 ${selectedImage === book.coverUrl ? 'ring-2 ring-purple-600 scale-105' : 'hover:scale-105'}`}
                    onClick={() => handleBookSelect(book)}
                    onKeyDown={(e) => e.key === 'Enter' && handleBookSelect(book)}
                    tabIndex={0}
                    role="button"
                    aria-label={`Select ${book.title} as profile picture`}
                  >
                    <div className="relative w-full" style={{ paddingBottom: '160%' }}>
                      <Image
                        src={book.coverUrl}
                        alt={book.title}
                        fill
                        sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw"
                        className="object-cover rounded-md"
                      />
                    </div>
                    <p className="mt-1 text-sm text-gray-700 truncate">{book.title}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Coming Soon Books Tab */}
        {activeTab === 'comingSoon' && (
          <div>
            <div className="mb-4">
              <h3 className="text-lg font-medium text-gray-800 mb-2">Upcoming Book Releases</h3>
              <p className="text-sm text-gray-600">
                Show your excitement for upcoming books by using their covers as your profile picture.
                <span className="font-medium"> The number badge shows how many readers are using this cover.</span>
              </p>
            </div>

            {isLoadingBooks ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-600 mx-auto" />
                <p className="mt-2 text-gray-500">Loading upcoming books...</p>
              </div>
            ) : comingSoonBooks.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No upcoming books found.</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6">
                {comingSoonBooks.map(book => (
                  <div
                    key={book.id}
                    className={`cursor-pointer transition-all duration-200 ${selectedImage === book.coverUrl ? 'ring-2 ring-purple-600 scale-105' : 'hover:scale-105'}`}
                    onClick={() => handleBookSelect(book)}
                    onKeyDown={(e) => e.key === 'Enter' && handleBookSelect(book)}
                    tabIndex={0}
                    role="button"
                    aria-label={`Select ${book.title} as profile picture`}
                  >
                    <div className="relative">
                      <div className="relative w-full" style={{ paddingBottom: '160%' }}>
                        <Image
                          src={book.coverUrl}
                          alt={book.title}
                          fill
                          sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw"
                          className="object-cover rounded-md"
                        />
                      </div>

                      {/* Usage count badge */}
                      {book.usageCount && book.usageCount > 0 && (
                        <div
                          className="absolute -bottom-2 -right-2 bg-purple-600 text-white text-xs font-bold rounded-full flex items-center justify-center"
                          style={{
                            width: '24px',
                            height: '24px',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                          }}
                        >
                          {book.usageCount}
                        </div>
                      )}

                      {/* Release date badge */}
                      {book.releaseDate && (
                        <div className="absolute top-0 right-0 bg-black bg-opacity-70 text-white text-xs px-2 py-1 m-1 rounded">
                          {new Date(book.releaseDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                        </div>
                      )}
                    </div>
                    <p className="mt-1 text-sm font-medium text-gray-800 truncate">{book.title}</p>
                    {book.author && (
                      <p className="text-xs text-gray-600 truncate">by {book.author.name}</p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Authors Tab */}
        {activeTab === 'authors' && (
          <div>
            <div className="mb-4">
              <h3 className="text-lg font-medium text-gray-800 mb-2">Browse by Author</h3>
              <p className="text-sm text-gray-600">
                Select an author to see their upcoming books
              </p>
            </div>

            {selectedAuthor ? (
              <div>
                <div className="flex items-center mb-4">
                  <button
                    type="button"
                    onClick={() => setSelectedAuthor(null)}
                    className="text-purple-600 hover:text-purple-800 font-medium text-sm flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                    Back to Authors
                  </button>
                </div>

                <div className="flex items-center mb-6">
                  {selectedAuthor.photoUrl ? (
                    <div className="w-12 h-12 rounded-full overflow-hidden mr-3">
                      <Image
                        src={selectedAuthor.photoUrl}
                        alt={selectedAuthor.name}
                        width={48}
                        height={48}
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center mr-3">
                      {selectedAuthor.name.charAt(0)}
                    </div>
                  )}
                  <div>
                    <h3 className="font-medium text-gray-900">{selectedAuthor.name}</h3>
                    <p className="text-sm text-gray-500">{selectedAuthor.comingSoonBooks.length} upcoming {selectedAuthor.comingSoonBooks.length === 1 ? 'book' : 'books'}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6">
                  {selectedAuthor.comingSoonBooks.map(book => (
                    <div
                      key={book.id}
                      className={`cursor-pointer transition-all duration-200 ${selectedImage === book.coverUrl ? 'ring-2 ring-purple-600 scale-105' : 'hover:scale-105'}`}
                      onClick={() => handleBookSelect(book)}
                      onKeyDown={(e) => e.key === 'Enter' && handleBookSelect(book)}
                      tabIndex={0}
                      role="button"
                      aria-label={`Select ${book.title} as profile picture`}
                    >
                      <div className="relative">
                        <div className="relative w-full" style={{ paddingBottom: '160%' }}>
                          <Image
                            src={book.coverUrl}
                            alt={book.title}
                            fill
                            sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw"
                            className="object-cover rounded-md"
                          />
                        </div>

                        {/* Usage count badge */}
                        {book.usageCount && book.usageCount > 0 && (
                          <div
                            className="absolute -bottom-2 -right-2 bg-purple-600 text-white text-xs font-bold rounded-full flex items-center justify-center"
                            style={{
                              width: '24px',
                              height: '24px',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                            }}
                          >
                            {book.usageCount}
                          </div>
                        )}

                        {/* Release date badge */}
                        {book.releaseDate && (
                          <div className="absolute top-0 right-0 bg-black bg-opacity-70 text-white text-xs px-2 py-1 m-1 rounded">
                            {new Date(book.releaseDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                          </div>
                        )}
                      </div>
                      <p className="mt-1 text-sm font-medium text-gray-800 truncate">{book.title}</p>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {authors.map(author => (
                  <div
                    key={author.id}
                    className="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                    onClick={() => handleAuthorSelect(author)}
                    onKeyDown={(e) => e.key === 'Enter' && handleAuthorSelect(author)}
                    tabIndex={0}
                    role="button"
                    aria-label={`View books by ${author.name}`}
                  >
                    {author.photoUrl ? (
                      <div className="w-12 h-12 rounded-full overflow-hidden mr-3">
                        <Image
                          src={author.photoUrl}
                          alt={author.name}
                          width={48}
                          height={48}
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-12 h-12 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center mr-3">
                        {author.name.charAt(0)}
                      </div>
                    )}
                    <div>
                      <h3 className="font-medium text-gray-900">{author.name}</h3>
                      <p className="text-sm text-gray-500">{author.comingSoonBooks.length} upcoming {author.comingSoonBooks.length === 1 ? 'book' : 'books'}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Browse Book Covers Tab */}
        {activeTab === 'allBooks' && (
          <div>
            <div className="flex mb-4">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search books..."
                className="flex-1 px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent"
              />
              <button
                type="button"
                onClick={handleSearch}
                className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-r-md transition-colors"
                disabled={isLoadingBooks}
              >
                {isLoadingBooks ? 'Searching...' : 'Search'}
              </button>
            </div>

            {isLoadingBooks ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-600 mx-auto" />
                <p className="mt-2 text-gray-500">Searching books...</p>
              </div>
            ) : allBooks.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">Search for books to find a cover you like.</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {allBooks.map(book => (
                  <div
                    key={book.id}
                    className={`cursor-pointer transition-all duration-200 ${selectedImage === book.coverUrl ? 'ring-2 ring-purple-600 scale-105' : 'hover:scale-105'}`}
                    onClick={() => handleBookSelect(book)}
                    onKeyDown={(e) => e.key === 'Enter' && handleBookSelect(book)}
                    tabIndex={0}
                    role="button"
                    aria-label={`Select ${book.title} as profile picture`}
                  >
                    <div className="relative">
                      <div className="relative w-full" style={{ paddingBottom: '160%' }}>
                        <Image
                          src={book.coverUrl}
                          alt={book.title}
                          fill
                          sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw"
                          className="object-cover rounded-md"
                        />
                      </div>

                      {/* Usage count badge */}
                      {book.usageCount && book.usageCount > 0 && (
                        <div
                          className="absolute -bottom-2 -right-2 bg-purple-600 text-white text-xs font-bold rounded-full flex items-center justify-center"
                          style={{
                            width: '24px',
                            height: '24px',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                          }}
                        >
                          {book.usageCount}
                        </div>
                      )}
                    </div>
                    <p className="mt-1 text-sm text-gray-700 truncate">{book.title}</p>
                    {book.author && (
                      <p className="text-xs text-gray-600 truncate">by {book.author.name}</p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-2 mt-4">
        <button
          type="button"
          onClick={handleCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Cancel
        </button>
        {!hideSaveButton && (
          <button
            type="button"
            onClick={handleSave}
            disabled={!selectedImage || isLoading}
            className="px-4 py-2 text-sm font-medium text-white bg-black border border-transparent rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save Profile Picture'}
          </button>
        )}
      </div>
    </div>
  );
}
