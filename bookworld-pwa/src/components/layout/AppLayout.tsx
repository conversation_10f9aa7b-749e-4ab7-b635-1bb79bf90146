import React, { ReactNode } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface AppLayoutProps {
  children: ReactNode;
}

export default function AppLayout({ children }: AppLayoutProps) {
  const pathname = usePathname();

  const navItems = [
    { name: 'Home', path: '/', icon: 'home' },
    { name: 'Discover', path: '/discover', icon: 'search' },
    { name: 'My Books', path: '/my-books', icon: 'book' },
    { name: 'Trophies', path: '/trophies', icon: 'trophy' },
    { name: 'Profile', path: '/profile', icon: 'user' },
  ];

  const isActive = (path: string) => {
    return pathname === path;
  };

  return (
    <div className="flex flex-col min-h-screen">
      {/* Main content */}
      <main className="flex-1 pb-16 md:pb-0 md:pl-16">
        {children}
      </main>

      {/* Mobile bottom navigation */}
      <nav className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 md:hidden">
        <div className="flex justify-around">
          {navItems.map((item) => (
            <Link
              key={item.path}
              href={item.path}
              className={`flex flex-col items-center justify-center py-2 px-3 ${
                isActive(item.path)
                  ? 'text-black'
                  : 'text-gray-500 hover:text-gray-900'
              }`}
            >
              <span className="text-2xl">
                {getIcon(item.icon, isActive(item.path))}
              </span>
              <span className="text-xs mt-1">{item.name}</span>
            </Link>
          ))}
        </div>
      </nav>

      {/* Desktop sidebar navigation */}
      <nav className="fixed top-0 left-0 bottom-0 hidden md:flex flex-col w-16 bg-white border-r border-gray-200">
        <div className="flex flex-col items-center justify-center py-4">
          <Link href="/" className="text-2xl font-bold mb-8">
            BW
          </Link>
          <div className="flex flex-col items-center space-y-8">
            {navItems.map((item) => (
              <Link
                key={item.path}
                href={item.path}
                className={`flex flex-col items-center justify-center p-2 rounded-md ${
                  isActive(item.path)
                    ? 'text-black bg-gray-100'
                    : 'text-gray-500 hover:text-gray-900 hover:bg-gray-50'
                }`}
                title={item.name}
              >
                <span className="text-2xl">
                  {getIcon(item.icon, isActive(item.path))}
                </span>
              </Link>
            ))}
          </div>
        </div>
      </nav>
    </div>
  );
}

function getIcon(name: string, isActive: boolean) {
  const activeClass = isActive ? 'font-bold' : '';
  
  switch (name) {
    case 'home':
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          className={`w-6 h-6 ${activeClass}`}
        >
          <path d="M11.47 3.84a.75.75 0 011.06 0l8.69 8.69a.75.75 0 101.06-1.06l-8.689-8.69a2.25 2.25 0 00-3.182 0l-8.69 8.69a.75.75 0 001.061 1.06l8.69-8.69z" />
          <path d="M12 5.432l8.159 8.159c.03.03.06.058.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 01-.75-.75v-4.5a.75.75 0 00-.75-.75h-3a.75.75 0 00-.75.75V21a.75.75 0 01-.75.75H5.625a1.875 1.875 0 01-1.875-1.875v-6.198a2.29 2.29 0 00.091-.086L12 5.43z" />
        </svg>
      );
    case 'search':
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          className={`w-6 h-6 ${activeClass}`}
        >
          <path
            fillRule="evenodd"
            d="M10.5 3.75a6.75 6.75 0 100 13.5 6.75 6.75 0 000-13.5zM2.25 10.5a8.25 8.25 0 1114.59 5.28l4.69 4.69a.75.75 0 11-1.06 1.06l-4.69-4.69A8.25 8.25 0 012.25 10.5z"
            clipRule="evenodd"
          />
        </svg>
      );
    case 'book':
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          className={`w-6 h-6 ${activeClass}`}
        >
          <path d="M11.25 4.533A9.707 9.707 0 006 3a9.735 9.735 0 00-3.25.555.75.75 0 00-.5.707v14.25a.75.75 0 001 .707A8.237 8.237 0 016 18.75c1.995 0 3.823.707 5.25 1.886V4.533zM12.75 20.636A8.214 8.214 0 0118 18.75c.966 0 1.89.166 2.75.47a.75.75 0 001-.708V4.262a.75.75 0 00-.5-.707A9.735 9.735 0 0018 3a9.707 9.707 0 00-5.25 1.533v16.103z" />
        </svg>
      );
    case 'trophy':
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          className={`w-6 h-6 ${activeClass}`}
        >
          <path
            fillRule="evenodd"
            d="M5.166 2.621v.858c-1.035.148-2.059.33-3.071.543a.75.75 0 00-.584.859 6.937 6.937 0 006.229 5.828c.15 0 .294-.014.434-.04v.841c0 .138.112.25.25.25H20.25a.25.25 0 00.25-.25v-.79a6.973 6.973 0 00.173-.022l.002-.001.026-.004.02-.003a.75.75 0 00-.583-.858 34.45 34.45 0 00-3.072-.543V2.62a.75.75 0 00-.75-.75H5.916a.75.75 0 00-.75.75zm.956 8.464a5.437 5.437 0 01-2.77-1.823 32.95 32.95 0 012.77-.499v2.322zm1.5-2.322v2.322c.985-.123 1.9-.277 2.77-.499a5.437 5.437 0 01-2.77-1.823zm5.302 2.322v-.864c.968.18 1.99.309 3.07.381a5.422 5.422 0 01-3.07.483zm3.07-1.496a34.047 34.047 0 01-3.07-.381V6.767c1.263.17 2.21.704 3.07 1.822zm-6.372-1.496a32.95 32.95 0 01-2.77.499V6.767a5.437 5.437 0 012.77 1.823zm-2.77 3.574c.908.13 1.837.229 2.77.28v2.148a5.066 5.066 0 01-2.77-2.428zm4.27.28c.933-.051 1.862-.15 2.77-.28a5.066 5.066 0 01-2.77 2.428v-2.148z"
            clipRule="evenodd"
          />
        </svg>
      );
    case 'user':
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          className={`w-6 h-6 ${activeClass}`}
        >
          <path
            fillRule="evenodd"
            d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z"
            clipRule="evenodd"
          />
        </svg>
      );
    default:
      return null;
  }
}
