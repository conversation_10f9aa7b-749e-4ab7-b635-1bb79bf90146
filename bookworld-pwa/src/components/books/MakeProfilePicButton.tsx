'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';
import { useAuthContext } from '@/lib/context/AuthContext';

interface MakeProfilePicButtonProps {
  bookId: string;
  coverUrl: string;
  usageCount: number;
  releaseDate?: Date;
  className?: string;
  variant?: 'default' | 'compact' | 'icon';
}

export default function MakeProfilePicButton({
  bookId,
  coverUrl,
  usageCount,
  releaseDate,
  className = '',
  variant = 'default'
}: MakeProfilePicButtonProps) {
  const router = useRouter();
  const { user } = useAuthContext();
  const [isLoading, setIsLoading] = useState(false);
  const [isUsingAsCover, setIsUsingAsCover] = useState(false);
  
  // Check if the release date is in the future
  const isComingSoon = releaseDate ? new Date(releaseDate) > new Date() : false;
  
  // Format the release date
  const formattedDate = releaseDate 
    ? new Date(releaseDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
    : '';
  
  const handleMakeProfilePic = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering parent click events
    
    if (!user) {
      router.push('/signin');
      return;
    }
    
    setIsLoading(true);
    
    try {
      // This would be replaced with an actual API call to update the user's profile picture
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate success
      setIsUsingAsCover(true);
      
      // In a real implementation, you would update the user's profile picture in the database
      // and increment the usage count for this book cover
      
    } catch (error) {
      console.error('Error setting profile picture:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  if (variant === 'icon') {
    return (
      <button
        type="button"
        onClick={handleMakeProfilePic}
        disabled={isLoading || isUsingAsCover}
        className={`flex items-center justify-center w-8 h-8 rounded-full bg-white bg-opacity-90 shadow-md hover:bg-opacity-100 transition-all ${className} ${isUsingAsCover ? 'text-purple-600' : 'text-gray-700'}`}
        title={isUsingAsCover ? 'Currently your profile picture' : 'Use as profile picture'}
        aria-label={isUsingAsCover ? 'Currently your profile picture' : 'Use as profile picture'}
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className="h-5 w-5" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
          aria-hidden="true"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d={isUsingAsCover 
              ? "M5 13l4 4L19 7" // Checkmark
              : "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" // User icon
            } 
          />
        </svg>
      </button>
    );
  }
  
  if (variant === 'compact') {
    return (
      <div className={`flex items-center ${className}`}>
        <button
          type="button"
          onClick={handleMakeProfilePic}
          disabled={isLoading || isUsingAsCover}
          className={`text-xs font-medium flex items-center ${isUsingAsCover ? 'text-purple-600' : 'text-gray-700 hover:text-purple-600'}`}
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-4 w-4 mr-1" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
            aria-hidden="true"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d={isUsingAsCover 
                ? "M5 13l4 4L19 7" // Checkmark
                : "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" // User icon
              } 
            />
          </svg>
          {isUsingAsCover ? 'Your Profile' : 'Make Profile Pic'}
        </button>
        {usageCount > 0 && (
          <span className="ml-2 text-xs text-gray-500 flex items-center">
            <span className="bg-purple-100 text-purple-600 rounded-full px-1.5 py-0.5 font-medium">
              {usageCount}
            </span>
          </span>
        )}
      </div>
    );
  }
  
  // Default variant
  return (
    <div className={`${className}`}>
      <div className="flex items-center justify-between mb-2">
        <Button
          size="sm"
          variant={isUsingAsCover ? "outline" : "default"}
          onClick={handleMakeProfilePic}
          disabled={isLoading || isUsingAsCover}
          isLoading={isLoading}
          className={isUsingAsCover ? "border-purple-600 text-purple-600" : ""}
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-4 w-4 mr-1" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
            aria-hidden="true"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d={isUsingAsCover 
                ? "M5 13l4 4L19 7" // Checkmark
                : "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" // User icon
              } 
            />
          </svg>
          {isUsingAsCover ? 'Your Profile Picture' : 'Make Profile Pic'}
        </Button>
        
        {usageCount > 0 && (
          <div className="flex items-center">
            <span className="text-sm text-gray-600 mr-1">Used by</span>
            <span className="bg-purple-100 text-purple-600 rounded-full px-2 py-0.5 text-sm font-medium">
              {usageCount}
            </span>
          </div>
        )}
      </div>
      
      {isComingSoon && (
        <div className="text-xs text-gray-500">
          Coming {formattedDate}
        </div>
      )}
    </div>
  );
}
