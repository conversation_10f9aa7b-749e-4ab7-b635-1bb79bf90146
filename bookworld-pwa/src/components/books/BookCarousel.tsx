'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { BookData } from './BookCard';
import Button from '@/components/ui/Button';
import { formatDate } from '@/lib/utils/format';

interface BookCarouselProps {
  books: BookData[];
  className?: string;
}

export default function BookCarousel({ books, className = '' }: BookCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [startX, setStartX] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const carouselRef = useRef<HTMLDivElement>(null);
  
  const handlePrev = () => {
    setCurrentIndex(prev => (prev === 0 ? books.length - 1 : prev - 1));
  };
  
  const handleNext = () => {
    setCurrentIndex(prev => (prev === books.length - 1 ? 0 : prev + 1));
  };
  
  const handleDotClick = (index: number) => {
    setCurrentIndex(index);
  };
  
  // Touch and mouse drag handlers
  const handleDragStart = (clientX: number) => {
    setStartX(clientX);
    setIsDragging(true);
  };
  
  const handleDragMove = (clientX: number) => {
    if (!isDragging) return;
    
    const deltaX = clientX - startX;
    
    if (Math.abs(deltaX) > 50) {
      if (deltaX > 0) {
        handlePrev();
      } else {
        handleNext();
      }
      setIsDragging(false);
    }
  };
  
  const handleDragEnd = () => {
    setIsDragging(false);
  };
  
  // Auto-advance the carousel
  useEffect(() => {
    const interval = setInterval(() => {
      handleNext();
    }, 5000);
    
    return () => clearInterval(interval);
  }, [currentIndex]);
  
  // Get current book
  const currentBook = books[currentIndex];
  
  // Calculate bestseller rank display
  const getBestsellerRankDisplay = (book: BookData) => {
    if (!book.bestsellerRank) return null;
    
    let rankColor = '#6366F1'; // Default indigo
    
    if (book.bestsellerRank <= 10) {
      rankColor = 'linear-gradient(135deg, #D4AF37 0%, #FFF0A3 50%, #D4AF37 100%)'; // Gold
    } else if (book.bestsellerRank <= 50) {
      rankColor = 'linear-gradient(135deg, #E5E4E2 0%, #FFFFFF 50%, #E5E4E2 100%)'; // Platinum
    } else if (book.bestsellerRank <= 100) {
      rankColor = 'linear-gradient(135deg, #B87333 0%, #FFC8A2 50%, #B87333 100%)'; // Bronze
    } else if (book.bestsellerRank <= 500) {
      rankColor = '#EF4444'; // Red
    } else if (book.bestsellerRank <= 1000) {
      rankColor = '#F59E0B'; // Amber
    }
    
    return {
      rank: book.bestsellerRank,
      category: book.bestsellerCategory || 'Overall',
      color: rankColor
    };
  };
  
  const bestsellerInfo = currentBook ? getBestsellerRankDisplay(currentBook) : null;
  
  if (books.length === 0) {
    return (
      <div className={`bg-gray-100 rounded-lg p-8 text-center ${className}`}>
        <p className="text-gray-500">No books to display</p>
      </div>
    );
  }
  
  return (
    <div 
      className={`relative overflow-hidden rounded-lg ${className}`}
      ref={carouselRef}
      onTouchStart={(e) => handleDragStart(e.touches[0].clientX)}
      onTouchMove={(e) => handleDragMove(e.touches[0].clientX)}
      onTouchEnd={() => handleDragEnd()}
      onMouseDown={(e) => handleDragStart(e.clientX)}
      onMouseMove={(e) => handleDragMove(e.clientX)}
      onMouseUp={() => handleDragEnd()}
      onMouseLeave={() => handleDragEnd()}
    >
      {/* Book display */}
      <div className="relative h-[500px] bg-gradient-to-b from-gray-900 to-gray-800">
        {/* Background blur effect with book cover */}
        <div className="absolute inset-0 opacity-30 blur-xl">
          <Image
            src={currentBook.coverUrl}
            alt={currentBook.title}
            fill
            className="object-cover"
          />
        </div>
        
        <div className="relative h-full flex flex-col md:flex-row items-center p-6 md:p-10">
          {/* Book cover */}
          <motion.div 
            className="relative w-48 h-72 md:w-64 md:h-96 flex-shrink-0 shadow-2xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            key={currentBook.id}
          >
            <Image
              src={currentBook.coverUrl}
              alt={currentBook.title}
              fill
              className="object-cover rounded-md"
              priority
            />
            
            {/* Release date badge */}
            <div className="absolute top-0 right-0 bg-black bg-opacity-70 text-white text-xs px-2 py-1 m-2 rounded">
              Released {formatDate(currentBook.publishedAt, { month: 'short', day: 'numeric', year: 'numeric' })}
            </div>
            
            {/* Bestseller rank badge */}
            {bestsellerInfo && (
              <div 
                className="absolute bottom-0 left-0 right-0 py-2 px-3 bg-black bg-opacity-80 text-white"
                style={{
                  background: typeof bestsellerInfo.color === 'string' && bestsellerInfo.color.startsWith('linear-gradient')
                    ? bestsellerInfo.color
                    : `linear-gradient(90deg, ${bestsellerInfo.color} 0%, rgba(0,0,0,0.8) 100%)`
                }}
              >
                <div className="text-xs font-bold">
                  #{bestsellerInfo.rank} Bestseller
                </div>
                <div className="text-xs opacity-80">
                  in {bestsellerInfo.category}
                </div>
              </div>
            )}
          </motion.div>
          
          {/* Book details */}
          <motion.div 
            className="flex-1 text-white md:pl-10 pt-6 md:pt-0"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            key={`details-${currentBook.id}`}
          >
            <h2 className="text-2xl md:text-4xl font-bold mb-2">{currentBook.title}</h2>
            <p className="text-gray-300 mb-4">{currentBook.author.name}</p>
            
            {/* Tags */}
            {currentBook.tags && currentBook.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {currentBook.tags.map(tag => (
                  <span 
                    key={tag} 
                    className="text-xs bg-gray-700 text-gray-200 px-2 py-1 rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}
            
            {/* Description */}
            <p className="text-gray-300 mb-6 line-clamp-3 md:line-clamp-none">
              {currentBook.description}
            </p>
            
            {/* Rating */}
            {currentBook.rating && (
              <div className="flex items-center mb-6">
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <span key={star} className="text-xl">
                      {star <= Math.round(currentBook.rating) ? '★' : '☆'}
                    </span>
                  ))}
                </div>
                <span className="ml-2 text-gray-300">
                  {currentBook.rating.toFixed(1)}
                  {currentBook.reviewCount && (
                    <span className="ml-1">({currentBook.reviewCount} reviews)</span>
                  )}
                </span>
              </div>
            )}
            
            {/* Action buttons */}
            <div className="flex flex-wrap gap-3">
              <Button size="lg">
                Read Three Chapters
              </Button>
              <Button size="lg" variant="outline">
                {currentBook.price === 0 ? 'Get for Free' : `Buy for $${currentBook.price.toFixed(2)}`}
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
      
      {/* Navigation arrows */}
      <button
        onClick={handlePrev}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
        aria-label="Previous book"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      <button
        onClick={handleNext}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
        aria-label="Next book"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>
      
      {/* Dots navigation */}
      <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
        {books.map((_, index) => (
          <button
            key={index}
            onClick={() => handleDotClick(index)}
            className={`w-2 h-2 rounded-full transition-all ${
              index === currentIndex ? 'bg-white w-4' : 'bg-white bg-opacity-50'
            }`}
            aria-label={`Go to book ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
}
