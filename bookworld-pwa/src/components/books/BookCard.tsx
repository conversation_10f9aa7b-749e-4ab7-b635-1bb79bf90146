'use client';

import type { MouseEvent } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import MakeProfilePicButton from './MakeProfilePicButton';
import { formatCurrency } from '@/lib/utils/format';

export interface BookData {
  id: string;
  title: string;
  author: {
    id: string;
    name: string;
    photoUrl?: string;
  };
  coverUrl: string;
  price: number; // 0 for free books
  description: string;
  rating?: number;
  reviewCount?: number;
  publishedAt: Date;
  tags?: string[];
  usageCount?: number;
  releaseDate?: Date;
  isComingSoon?: boolean;
  bestsellerRank?: number;
  bestsellerCategory?: string;
}

interface BookCardProps {
  book: BookData;
  variant?: 'default' | 'compact' | 'grid';
  showBuyButton?: boolean;
  showReadButton?: boolean;
  showProfilePicButton?: boolean;
  isPurchased?: boolean;
}

export default function BookCard({
  book,
  variant = 'default',
  showBuyButton = false,
  showReadButton = false,
  showProfilePicButton = false,
  isPurchased = false,
}: BookCardProps) {
  const router = useRouter();

  const handleViewBook = () => {
    router.push(`/books/${book.id}`);
  };

  const handleReadBook = () => {
    router.push(`/reader/${book.id}`);
  };

  const handleBuyBook = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push(`/checkout/${book.id}`);
  };

  if (variant === 'compact') {
    return (
      <div
        className="flex items-center space-x-3 p-2 cursor-pointer hover:bg-gray-50 rounded-md transition-colors"
        onClick={handleViewBook}
        onKeyDown={(e) => e.key === 'Enter' && handleViewBook()}
        tabIndex={0}
        role="button"
        aria-label={`View details for ${book.title}`}
      >
        <div className="relative h-16 w-12 flex-shrink-0">
          <Image
            src={book.coverUrl}
            alt={book.title}
            fill
            className="object-cover rounded-sm"
          />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-gray-900 truncate">{book.title}</h3>
          <p className="text-xs text-gray-500 truncate">{book.author.name}</p>
          <p className="text-xs font-medium mt-1">
            {book.price === 0 ? 'Free' : formatCurrency(book.price)}
          </p>
        </div>
        {showReadButton && (
          <Button
            size="sm"
            variant="ghost"
            onClick={handleReadBook}
          >
            Read
          </Button>
        )}
      </div>
    );
  }

  if (variant === 'grid') {
    return (
      <div
        className="flex flex-col cursor-pointer group"
        onClick={handleViewBook}
        onKeyDown={(e) => e.key === 'Enter' && handleViewBook()}
        tabIndex={0}
        role="button"
        aria-label={`View details for ${book.title}`}
      >
        <div className="relative aspect-[2/3] w-full mb-2 rounded-md overflow-hidden">
          <Image
            src={book.coverUrl}
            alt={book.title}
            fill
            className="object-cover transition-transform group-hover:scale-105"
          />
        </div>
        <h3 className="text-sm font-medium text-gray-900 truncate">{book.title}</h3>
        <p className="text-xs text-gray-500 truncate">{book.author.name}</p>
        <div className="flex items-center justify-between mt-1">
          <p className="text-sm font-medium">
            {book.price === 0 ? 'Free' : formatCurrency(book.price)}
          </p>
          <div className="flex space-x-2">
            {showBuyButton && !isPurchased && (
              <Button
                size="xs"
                onClick={handleBuyBook}
              >
                {book.price === 0 ? 'Get' : 'Buy'}
              </Button>
            )}
            {showReadButton && isPurchased && (
              <Button
                size="xs"
                variant="outline"
                onClick={handleReadBook}
              >
                Read
              </Button>
            )}
            {showProfilePicButton && (
              <MakeProfilePicButton
                bookId={book.id}
                coverUrl={book.coverUrl}
                usageCount={book.usageCount || 0}
                releaseDate={book.releaseDate}
                variant="icon"
              />
            )}
          </div>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <Card className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow" onClick={handleViewBook}>
      <div className="flex md:flex-row flex-col">
        <div className="relative md:w-1/3 w-full aspect-[2/3] md:max-w-[180px]">
          <Image
            src={book.coverUrl}
            alt={book.title}
            fill
            className="object-cover"
          />
        </div>
        <div className="p-4 flex-1">
          <h3 className="text-lg font-bold mb-1">{book.title}</h3>
          <div className="flex items-center mb-2">
            {book.author.photoUrl ? (
              <Image
                src={book.author.photoUrl}
                alt={book.author.name}
                width={24}
                height={24}
                className="rounded-full mr-2"
              />
            ) : (
              <div className="w-6 h-6 rounded-full bg-gray-200 mr-2 flex items-center justify-center text-xs">
                {book.author.name.charAt(0)}
              </div>
            )}
            <span className="text-sm text-gray-700">{book.author.name}</span>
          </div>

          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {book.description}
          </p>

          {book.tags && book.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {book.tags.map(tag => (
                <span
                  key={tag}
                  className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          )}

          <div className="flex items-center justify-between mt-auto">
            <div>
              <p className="text-lg font-bold">
                {book.price === 0 ? 'Free' : formatCurrency(book.price)}
              </p>
              {book.rating && (
                <div className="flex items-center text-sm text-gray-500">
                  <span className="text-yellow-500 mr-1">★</span>
                  <span>{book.rating.toFixed(1)}</span>
                  {book.reviewCount && (
                    <span className="ml-1">({book.reviewCount})</span>
                  )}
                </div>
              )}
            </div>

            <div className="flex space-x-2">
              {showBuyButton && !isPurchased && (
                <Button
                  onClick={handleBuyBook}
                >
                  {book.price === 0 ? 'Get' : 'Buy'}
                </Button>
              )}
              {showReadButton && isPurchased && (
                <Button
                  variant="outline"
                  onClick={handleReadBook}
                >
                  Read
                </Button>
              )}
              {showProfilePicButton && (
                <MakeProfilePicButton
                  bookId={book.id}
                  coverUrl={book.coverUrl}
                  usageCount={book.usageCount || 0}
                  releaseDate={book.releaseDate}
                  variant="compact"
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
