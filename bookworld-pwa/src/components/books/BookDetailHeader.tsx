'use client';

import Image from 'next/image';
import { BookData } from './BookCard';
import Button from '@/components/ui/Button';
import MakeProfilePicButton from './MakeProfilePicButton';
import { formatCurrency, formatDate } from '@/lib/utils/format';

interface BookDetailHeaderProps {
  book: BookData;
  isPurchased?: boolean;
  onBuy?: () => void;
  onRead?: () => void;
}

export default function BookDetailHeader({
  book,
  isPurchased = false,
  onBuy,
  onRead
}: BookDetailHeaderProps) {
  const isComingSoon = book.releaseDate && new Date(book.releaseDate) > new Date();
  
  return (
    <div className="flex flex-col md:flex-row gap-6 mb-8">
      {/* Book Cover with Profile Pic Button */}
      <div className="md:w-1/3 max-w-[240px] mx-auto md:mx-0">
        <div className="relative">
          {/* Book Cover */}
          <div className="relative aspect-[2/3] w-full rounded-lg overflow-hidden shadow-lg">
            <Image
              src={book.coverUrl}
              alt={book.title}
              fill
              className="object-cover"
              priority
            />
            
            {/* Coming Soon Badge */}
            {isComingSoon && (
              <div className="absolute top-0 right-0 bg-black bg-opacity-70 text-white text-xs px-2 py-1 m-2 rounded">
                Coming {formatDate(book.releaseDate, { month: 'short', day: 'numeric', year: 'numeric' })}
              </div>
            )}
          </div>
          
          {/* Make Profile Pic Button */}
          <div className="mt-4">
            <MakeProfilePicButton
              bookId={book.id}
              coverUrl={book.coverUrl}
              usageCount={book.usageCount || 0}
              releaseDate={book.releaseDate}
            />
          </div>
        </div>
      </div>
      
      {/* Book Details */}
      <div className="flex-1">
        <h1 className="text-3xl font-bold mb-2">{book.title}</h1>
        
        {/* Author */}
        <div className="flex items-center mb-4">
          {book.author.photoUrl ? (
            <Image
              src={book.author.photoUrl}
              alt={book.author.name}
              width={32}
              height={32}
              className="rounded-full mr-2"
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-gray-200 mr-2 flex items-center justify-center text-xs">
              {book.author.name.charAt(0)}
            </div>
          )}
          <span className="text-lg text-gray-700">by {book.author.name}</span>
        </div>
        
        {/* Tags */}
        {book.tags && book.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {book.tags.map(tag => (
              <span 
                key={tag} 
                className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
        
        {/* Description */}
        <p className="text-gray-600 mb-6">
          {book.description}
        </p>
        
        {/* Price and Buttons */}
        <div className="flex items-center justify-between">
          <div>
            <p className="text-2xl font-bold mb-1">
              {book.price === 0 ? 'Free' : formatCurrency(book.price)}
            </p>
            {book.rating && (
              <div className="flex items-center text-sm text-gray-500">
                <span className="text-yellow-500 mr-1">★</span>
                <span>{book.rating.toFixed(1)}</span>
                {book.reviewCount && (
                  <span className="ml-1">({book.reviewCount})</span>
                )}
              </div>
            )}
          </div>
          
          <div className="flex space-x-3">
            {!isPurchased && onBuy && (
              <Button
                onClick={onBuy}
                size="lg"
              >
                {book.price === 0 ? 'Get for Free' : `Buy for ${formatCurrency(book.price)}`}
              </Button>
            )}
            
            {isPurchased && onRead && (
              <Button
                onClick={onRead}
                size="lg"
              >
                Read Now
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
