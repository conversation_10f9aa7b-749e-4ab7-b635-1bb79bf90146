'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { formatRelativeTime } from '@/lib/utils/format';

interface Post {
  id: string;
  authorId: string;
  authorName: string;
  authorPhotoUrl?: string;
  content: string;
  audioUrl?: string;
  imageUrl?: string;
  timestamp: Date;
  likes: number;
  comments: number;
  isLiked?: boolean;
}

interface AuthorSocialFeedProps {
  authorId: string;
  className?: string;
}

export default function AuthorSocialFeed({ authorId, className = '' }: AuthorSocialFeedProps) {
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isPlaying, setIsPlaying] = useState<string | null>(null);
  const [expandedPost, setExpandedPost] = useState<string | null>(null);
  const audioRefs = useRef<Record<string, HTMLAudioElement | null>>({});

  // Fetch posts
  useEffect(() => {
    const fetchPosts = async () => {
      setIsLoading(true);

      try {
        // This would be replaced with an actual API call
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock data
        const mockPosts: Post[] = [
          {
            id: 'p1',
            authorId,
            authorName: 'Emily Chen',
            authorPhotoUrl: '/images/profile-placeholder.svg',
            content: "I'm thrilled to announce that my new book 'The Dragon's Legacy' is now available! This has been a labor of love for the past two years, and I can't wait for you all to read it. The story follows a young dragon rider who discovers a hidden legacy that could change the fate of her kingdom.",
            audioUrl: '/audio/sample-post-1.mp3',
            imageUrl: 'https://via.placeholder.com/600x400/3949AB/FFFFFF?text=Dragon+Legacy+Announcement',
            timestamp: new Date(Date.now() - 86400000 * 2), // 2 days ago
            likes: 128,
            comments: 42,
            isLiked: false
          },
          {
            id: 'p2',
            authorId,
            authorName: 'Emily Chen',
            authorPhotoUrl: '/images/profile-placeholder.svg',
            content: "Just finished the first draft of 'Whispers in the Dark' sequel! It's been an intense writing journey, but I'm so excited about where these characters are headed. Thank you all for your patience and support!",
            audioUrl: '/audio/sample-post-2.mp3',
            timestamp: new Date(Date.now() - 86400000 * 5), // 5 days ago
            likes: 95,
            comments: 23,
            isLiked: true
          },
          {
            id: 'p3',
            authorId,
            authorName: 'Emily Chen',
            authorPhotoUrl: '/images/profile-placeholder.svg',
            content: "Writing update: 'Chronicles of the Forgotten' is now 75% complete! This epic fantasy has taken me to places I never expected. The ancient civilization at the heart of this story has so many secrets to reveal. Stay tuned for more updates!",
            audioUrl: '/audio/sample-post-3.mp3',
            imageUrl: 'https://via.placeholder.com/600x400/388E3C/FFFFFF?text=Writing+Progress',
            timestamp: new Date(Date.now() - 86400000 * 10), // 10 days ago
            likes: 76,
            comments: 18,
            isLiked: false
          }
        ];

        setPosts(mockPosts);
      } catch (error) {
        console.error('Error fetching posts:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPosts();
  }, [authorId]);

  // Handle audio playback
  const toggleAudio = (postId: string) => {
    const audioElement = audioRefs.current[postId];

    if (!audioElement) return;

    if (isPlaying === postId) {
      audioElement.pause();
      setIsPlaying(null);
    } else {
      // Pause any currently playing audio
      if (isPlaying && audioRefs.current[isPlaying]) {
        audioRefs.current[isPlaying]?.pause();
      }

      audioElement.play();
      setIsPlaying(postId);
    }
  };

  // Handle post expansion
  const toggleExpandPost = (postId: string) => {
    setExpandedPost(expandedPost === postId ? null : postId);
  };

  // Handle like
  const handleLike = (postId: string) => {
    setPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? {
              ...post,
              isLiked: !post.isLiked,
              likes: post.isLiked ? post.likes - 1 : post.likes + 1
            }
          : post
      )
    );
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Author Updates</h2>
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map(i => (
            <div key={i} className="animate-pulse">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gray-200 rounded-full mr-3" />
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-2" />
                  <div className="h-3 bg-gray-200 rounded w-1/6" />
                </div>
              </div>
              <div className="h-4 bg-gray-200 rounded mb-2" />
              <div className="h-4 bg-gray-200 rounded mb-2 w-3/4" />
              <div className="h-4 bg-gray-200 rounded w-1/2" />
              <div className="h-40 bg-gray-200 rounded mt-4" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      <div className="flex justify-between items-center p-6 border-b">
        <h2 className="text-2xl font-bold">Author Updates</h2>
      </div>

      <div className="divide-y">
        {posts.map(post => (
          <div key={post.id} className="p-6">
            {/* Post header */}
            <div className="flex items-center mb-4">
              <div className="relative w-12 h-12 rounded-full overflow-hidden mr-3">
                <Image
                  src={post.authorPhotoUrl || '/images/profile-placeholder.svg'}
                  alt={post.authorName}
                  fill
                  className="object-cover"
                />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{post.authorName}</h3>
                <p className="text-sm text-gray-500">{formatRelativeTime(post.timestamp)}</p>
              </div>
            </div>

            {/* Post content */}
            <div className="mb-4">
              <p className={`text-gray-800 ${expandedPost !== post.id && post.content.length > 200 ? 'line-clamp-3' : ''}`}>
                {post.content}
              </p>
              {post.content.length > 200 && (
                <button
                  type="button"
                  onClick={() => toggleExpandPost(post.id)}
                  className="text-purple-600 text-sm font-medium mt-1 hover:text-purple-800"
                >
                  {expandedPost === post.id ? 'Show less' : 'Read more'}
                </button>
              )}
            </div>

            {/* Post image */}
            {post.imageUrl && (
              <div className="relative aspect-video w-full mb-4 rounded-lg overflow-hidden">
                <Image
                  src={post.imageUrl}
                  alt="Post image"
                  fill
                  className="object-cover"
                />
              </div>
            )}

            {/* Audio player */}
            {post.audioUrl && (
              <div className="mb-4">
                <audio
                  ref={(el) => { audioRefs.current[post.id] = el; }}
                  src={post.audioUrl}
                  onEnded={() => setIsPlaying(null)}
                  className="hidden"
                >
                  <track kind="captions" src="" label="English captions" />
                </audio>
                <button
                  type="button"
                  onClick={() => toggleAudio(post.id)}
                  className={`flex items-center px-3 py-2 rounded-full ${
                    isPlaying === post.id
                      ? 'bg-purple-100 text-purple-700'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {isPlaying === post.id ? (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      Pause Audio
                    </>
                  ) : (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                      </svg>
                      Play Audio
                    </>
                  )}
                </button>
              </div>
            )}

            {/* Post actions */}
            <div className="flex items-center text-gray-500">
              <button
                type="button"
                onClick={() => handleLike(post.id)}
                className={`flex items-center mr-4 ${post.isLiked ? 'text-purple-600' : ''}`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z" />
                </svg>
                {post.likes}
              </button>
              <button
                type="button"
                className="flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z" clipRule="evenodd" />
                </svg>
                {post.comments}
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
