'use client';

import { useState } from 'react';
import Image from 'next/image';
import Button from '@/components/ui/Button';

interface BookMailSubscribeModalProps {
  authorName: string;
  authorPhotoUrl?: string;
  isOpen: boolean;
  onClose: () => void;
  onSubscribe: (options: { followAuthor: boolean; subscribeToBookMail: boolean }) => void;
}

export default function BookMailSubscribeModal({
  authorName,
  authorPhotoUrl,
  isOpen,
  onClose,
  onSubscribe
}: BookMailSubscribeModalProps) {
  const [followAuthor, setFollowAuthor] = useState(true);
  const [subscribeToBookMail, setSubscribeToBookMail] = useState(true);
  
  if (!isOpen) return null;
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubscribe({ followAuthor, subscribeToBookMail });
  };
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div 
        className="bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-indigo-600 p-6 text-white">
          <div className="flex items-center">
            <div className="relative w-12 h-12 rounded-full overflow-hidden mr-4 ring-2 ring-white ring-opacity-50">
              <Image
                src={authorPhotoUrl || '/images/profile-placeholder.svg'}
                alt={authorName}
                fill
                className="object-cover"
              />
            </div>
            <div>
              <h2 className="text-xl font-bold">{authorName}'s BookMail</h2>
              <p className="text-sm text-purple-100">Exclusive updates directly from the author</p>
            </div>
          </div>
        </div>
        
        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="mb-6">
            <p className="text-gray-700 mb-4">
              Join {authorName}'s BookMail to receive exclusive updates, behind-the-scenes content, and early access to new releases.
            </p>
            
            <div className="bg-purple-50 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-purple-800 mb-2">What is BookMail?</h3>
              <p className="text-sm text-gray-700">
                BookMail is a premium feature that delivers author updates directly to you via push notifications. Unlike traditional mailing lists, BookMail is lightweight and respectful of your attention.
              </p>
            </div>
            
            <div className="space-y-3">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={followAuthor}
                  onChange={() => setFollowAuthor(!followAuthor)}
                  className="h-5 w-5 text-purple-600 rounded focus:ring-purple-500"
                />
                <span className="ml-2 text-gray-700">Follow {authorName}</span>
              </label>
              
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={subscribeToBookMail}
                  onChange={() => setSubscribeToBookMail(!subscribeToBookMail)}
                  className="h-5 w-5 text-purple-600 rounded focus:ring-purple-500"
                />
                <span className="ml-2 text-gray-700">Subscribe to {authorName}'s BookMail</span>
              </label>
            </div>
          </div>
          
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!followAuthor && !subscribeToBookMail}
            >
              {followAuthor && subscribeToBookMail 
                ? 'Follow & Subscribe' 
                : followAuthor 
                  ? 'Follow Author' 
                  : subscribeToBookMail 
                    ? 'Subscribe to BookMail' 
                    : 'Select an option'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
