'use client';

import { useEffect, useRef, useState } from 'react';
import { motion, useAnimation, AnimatePresence } from 'framer-motion';

interface InkPenFollowerCounterProps {
  followerCount: number;
  className?: string;
}

// Define color tiers for the ink pen
const colorTiers = [
  { threshold: 0, color: '#6366F1', name: 'Amethyst Ink' }, // Indigo
  { threshold: 100, color: '#8B5CF6', name: 'Royal Violet' }, // Purple
  { threshold: 500, color: '#EC4899', name: '<PERSON>' }, // Pink
  { threshold: 1000, color: '#EF4444', name: 'Phoenix Fire' }, // Red
  { threshold: 5000, color: '#F59E0B', name: '<PERSON> Flare' }, // Amber
  { threshold: 10000, color: '#10B981', name: 'Emerald Tide' }, // Emerald
  { threshold: 50000, color: '#3B82F6', name: 'Sapphire Wave' }, // Blue
  { threshold: 100000, color: 'linear-gradient(135deg, #B794F4 0%, #F687B3 50%, #FBBF24 100%)', name: 'Cosmic Fusion' }, // Gradient
  { threshold: 500000, color: 'linear-gradient(135deg, #D4AF37 0%, #FFF0A3 50%, #D4AF37 100%)', name: '14K Gold' }, // Gold
  { threshold: 1000000, color: 'linear-gradient(135deg, #E5E4E2 0%, #FFFFFF 50%, #E5E4E2 100%)', name: 'Platinum Elite' }, // Platinum
];

// Function to get the appropriate color based on follower count
const getColorTier = (count: number) => {
  for (let i = colorTiers.length - 1; i >= 0; i--) {
    if (count >= colorTiers[i].threshold) {
      return colorTiers[i];
    }
  }
  return colorTiers[0];
};

export default function InkPenFollowerCounter({ 
  followerCount, 
  className = '' 
}: InkPenFollowerCounterProps) {
  const controls = useAnimation();
  const [displayCount, setDisplayCount] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [colorTier, setColorTier] = useState(getColorTier(0));
  const [showTierName, setShowTierName] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  // Animate the follower count on mount and when it changes
  useEffect(() => {
    setIsAnimating(true);
    
    // Animate the counter
    let start = displayCount;
    const end = followerCount;
    const duration = 1500; // ms
    const frameDuration = 1000 / 60; // 60fps
    const totalFrames = Math.round(duration / frameDuration);
    const increment = (end - start) / totalFrames;
    
    let frame = 0;
    const counter = setInterval(() => {
      frame++;
      const newCount = Math.round(start + increment * frame);
      setDisplayCount(newCount);
      
      if (frame === totalFrames) {
        clearInterval(counter);
        setIsAnimating(false);
        setColorTier(getColorTier(end));
      }
    }, frameDuration);
    
    return () => clearInterval(counter);
  }, [followerCount]);
  
  // Draw the ink pen animation
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Set up pen dimensions
    const penWidth = canvas.width * 0.15;
    const penHeight = canvas.height * 0.7;
    const penX = canvas.width / 2 - penWidth / 2;
    const penY = canvas.height * 0.15;
    
    // Draw pen body
    ctx.fillStyle = '#333';
    ctx.beginPath();
    ctx.roundRect(penX, penY, penWidth, penHeight, [5, 5, 0, 0]);
    ctx.fill();
    
    // Draw pen tip
    ctx.beginPath();
    ctx.moveTo(penX, penY + penHeight);
    ctx.lineTo(penX + penWidth / 2, penY + penHeight + penWidth);
    ctx.lineTo(penX + penWidth, penY + penHeight);
    ctx.closePath();
    ctx.fill();
    
    // Draw ink color
    const inkColor = typeof colorTier.color === 'string' && colorTier.color.startsWith('linear-gradient') 
      ? '#8B5CF6' // Fallback for gradient in canvas
      : colorTier.color;
    
    ctx.fillStyle = inkColor;
    
    // Draw ink drop animation
    if (isAnimating) {
      const dropSize = penWidth * 0.6;
      const dropX = penX + penWidth / 2 - dropSize / 2;
      const dropY = penY + penHeight + penWidth + 10;
      
      ctx.beginPath();
      ctx.ellipse(
        dropX + dropSize / 2, 
        dropY, 
        dropSize / 2, 
        dropSize * (0.5 + Math.sin(Date.now() / 200) * 0.2), 
        0, 0, Math.PI * 2
      );
      ctx.fill();
    }
    
    // Draw ink in pen tip
    ctx.beginPath();
    ctx.moveTo(penX + penWidth * 0.3, penY + penHeight);
    ctx.lineTo(penX + penWidth / 2, penY + penHeight + penWidth * 0.7);
    ctx.lineTo(penX + penWidth * 0.7, penY + penHeight);
    ctx.closePath();
    ctx.fill();
    
    // Animation loop
    const animationFrame = requestAnimationFrame(() => {
      if (canvasRef.current) {
        // Redraw only if still animating
        if (isAnimating) {
          const currentEffect = () => {
            if (canvasRef.current) {
              const canvas = canvasRef.current;
              const ctx = canvas.getContext('2d');
              if (ctx) {
                // Redraw the animation
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                // ... (redraw code)
              }
            }
          };
          currentEffect();
        }
      }
    });
    
    return () => cancelAnimationFrame(animationFrame);
  }, [isAnimating, colorTier]);
  
  return (
    <div 
      className={`relative flex flex-col items-center ${className}`}
      onMouseEnter={() => setShowTierName(true)}
      onMouseLeave={() => setShowTierName(false)}
    >
      <div className="relative">
        <canvas 
          ref={canvasRef} 
          width={120} 
          height={200}
          className="mb-2"
        />
        
        {/* Follower count with ink color */}
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-full text-center">
          <AnimatePresence>
            <motion.div
              key={displayCount}
              initial={{ opacity: 0, y: -5 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="font-bold text-2xl"
              style={{ 
                color: typeof colorTier.color === 'string' && colorTier.color.startsWith('linear-gradient')
                  ? 'transparent'
                  : colorTier.color,
                background: typeof colorTier.color === 'string' && colorTier.color.startsWith('linear-gradient')
                  ? colorTier.color
                  : 'none',
                WebkitBackgroundClip: typeof colorTier.color === 'string' && colorTier.color.startsWith('linear-gradient') 
                  ? 'text' 
                  : 'border-box',
                WebkitTextFillColor: typeof colorTier.color === 'string' && colorTier.color.startsWith('linear-gradient')
                  ? 'transparent'
                  : 'inherit',
                textShadow: followerCount >= 500000 ? '0 0 5px rgba(255,255,255,0.5)' : 'none'
              }}
            >
              {displayCount.toLocaleString()}
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
      
      <div className="text-center">
        <span className="text-gray-600 font-medium">Followers</span>
        
        {/* Tier name tooltip */}
        <AnimatePresence>
          {showTierName && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="absolute mt-1 px-2 py-1 bg-black bg-opacity-80 text-white text-xs rounded-md whitespace-nowrap"
              style={{
                left: '50%',
                transform: 'translateX(-50%)',
                zIndex: 10
              }}
            >
              {colorTier.name} Tier
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
