'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface WorksCounterProps {
  worksCount: number;
  className?: string;
}

// Define color tiers for the works counter
const colorTiers = [
  { threshold: 0, color: '#6366F1', name: '<PERSON><PERSON>' }, // Indigo
  { threshold: 3, color: '#8B5CF6', name: 'Apprentice' }, // Purple
  { threshold: 5, color: '#EC4899', name: 'Journey<PERSON>' }, // Pink
  { threshold: 10, color: '#EF4444', name: 'Ex<PERSON>' }, // Red
  { threshold: 15, color: '#F59E0B', name: 'Master' }, // Amber
  { threshold: 20, color: '#10B981', name: '<PERSON><PERSON>' }, // Emerald
  { threshold: 30, color: '#3B82F6', name: '<PERSON><PERSON>' }, // Blue
  { threshold: 50, color: 'linear-gradient(135deg, #B794F4 0%, #F687B3 50%, #FBBF24 100%)', name: 'Mythi<PERSON>' }, // Gradient
  { threshold: 75, color: 'linear-gradient(135deg, #D4AF37 0%, #FFF0A3 50%, #D4AF37 100%)', name: 'Golden Quill' }, // Gold
  { threshold: 100, color: 'linear-gradient(135deg, #E5E4E2 0%, #FFFFFF 50%, #E5E4E2 100%)', name: 'Immortal' }, // Platinum
];

// Function to get the appropriate color based on works count
const getColorTier = (count: number) => {
  for (let i = colorTiers.length - 1; i >= 0; i--) {
    if (count >= colorTiers[i].threshold) {
      return colorTiers[i];
    }
  }
  return colorTiers[0];
};

export default function WorksCounter({ 
  worksCount, 
  className = '' 
}: WorksCounterProps) {
  const [displayCount, setDisplayCount] = useState(0);
  const [colorTier, setColorTier] = useState(getColorTier(0));
  const [showTierName, setShowTierName] = useState(false);
  const [particles, setParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    size: number;
    color: string;
    velocity: { x: number; y: number };
    opacity: number;
  }>>([]);
  
  // Animate the works count on mount and when it changes
  useEffect(() => {
    // Animate the counter
    let start = displayCount;
    const end = worksCount;
    const duration = 1500; // ms
    const frameDuration = 1000 / 60; // 60fps
    const totalFrames = Math.round(duration / frameDuration);
    const increment = (end - start) / totalFrames;
    
    let frame = 0;
    const counter = setInterval(() => {
      frame++;
      const newCount = Math.round(start + increment * frame);
      setDisplayCount(newCount);
      
      if (frame === totalFrames) {
        clearInterval(counter);
        setColorTier(getColorTier(end));
        
        // Create particles for celebration if the count increased
        if (end > start) {
          createParticles();
        }
      }
    }, frameDuration);
    
    return () => clearInterval(counter);
  }, [worksCount]);
  
  // Create particles for animation
  const createParticles = () => {
    const newParticles = [];
    const particleCount = 20;
    
    for (let i = 0; i < particleCount; i++) {
      newParticles.push({
        id: Date.now() + i,
        x: 50, // Center X
        y: 50, // Center Y
        size: Math.random() * 6 + 2,
        color: typeof colorTier.color === 'string' && !colorTier.color.startsWith('linear-gradient')
          ? colorTier.color
          : getRandomColor(),
        velocity: {
          x: (Math.random() - 0.5) * 4,
          y: (Math.random() - 0.5) * 4
        },
        opacity: 1
      });
    }
    
    setParticles(newParticles);
    
    // Animate particles
    const animationDuration = 1500; // ms
    const interval = 16; // ~60fps
    let elapsed = 0;
    
    const animateParticles = setInterval(() => {
      elapsed += interval;
      
      setParticles(prevParticles => 
        prevParticles.map(particle => ({
          ...particle,
          x: particle.x + particle.velocity.x,
          y: particle.y + particle.velocity.y,
          opacity: 1 - (elapsed / animationDuration)
        }))
      );
      
      if (elapsed >= animationDuration) {
        clearInterval(animateParticles);
        setParticles([]);
      }
    }, interval);
  };
  
  // Get random color for particles
  const getRandomColor = () => {
    const colors = ['#6366F1', '#8B5CF6', '#EC4899', '#EF4444', '#F59E0B', '#10B981', '#3B82F6'];
    return colors[Math.floor(Math.random() * colors.length)];
  };
  
  return (
    <div 
      className={`relative flex flex-col items-center ${className}`}
      onMouseEnter={() => setShowTierName(true)}
      onMouseLeave={() => setShowTierName(false)}
    >
      <div className="relative w-24 h-24 flex items-center justify-center">
        {/* Particles */}
        {particles.map(particle => (
          <div
            key={particle.id}
            className="absolute rounded-full"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: `${particle.size}px`,
              height: `${particle.size}px`,
              backgroundColor: particle.color,
              opacity: particle.opacity,
              transform: 'translate(-50%, -50%)'
            }}
          />
        ))}
        
        {/* Works count with dynamic color */}
        <AnimatePresence>
          <motion.div
            key={displayCount}
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ type: 'spring', stiffness: 300, damping: 20 }}
            className="font-bold text-5xl"
            style={{ 
              color: typeof colorTier.color === 'string' && colorTier.color.startsWith('linear-gradient')
                ? 'transparent'
                : colorTier.color,
              background: typeof colorTier.color === 'string' && colorTier.color.startsWith('linear-gradient')
                ? colorTier.color
                : 'none',
              WebkitBackgroundClip: typeof colorTier.color === 'string' && colorTier.color.startsWith('linear-gradient') 
                ? 'text' 
                : 'border-box',
              WebkitTextFillColor: typeof colorTier.color === 'string' && colorTier.color.startsWith('linear-gradient')
                ? 'transparent'
                : 'inherit',
              textShadow: worksCount >= 75 ? '0 0 5px rgba(255,255,255,0.5)' : 'none'
            }}
          >
            {displayCount}
          </motion.div>
        </AnimatePresence>
      </div>
      
      <div className="text-center mt-2">
        <span className="text-gray-600 font-medium">Works</span>
        
        {/* Tier name tooltip */}
        <AnimatePresence>
          {showTierName && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="absolute mt-1 px-2 py-1 bg-black bg-opacity-80 text-white text-xs rounded-md whitespace-nowrap"
              style={{
                left: '50%',
                transform: 'translateX(-50%)',
                zIndex: 10
              }}
            >
              {colorTier.name} Author
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
