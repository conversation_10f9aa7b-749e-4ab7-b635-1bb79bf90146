'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { formatRelativeTime } from '@/lib/utils/format';
import Button from '@/components/ui/Button';

export interface AudioPostData {
  id: string;
  authorId: string;
  authorName: string;
  authorPhotoUrl?: string;
  title: string;
  audioUrl: string;
  coverUrl?: string; // Optional for posts with image
  duration: number; // in seconds
  waveformData?: number[]; // For visualizing audio waveform
  likes: number;
  comments: number;
  createdAt: Date;
  isFollowing?: boolean;
}

interface AudioPostProps {
  post: AudioPostData;
  onLike: (postId: string) => void;
  onComment: (postId: string) => void;
  onFollow: (authorId: string) => void;
}

export default function AudioPost({ post, onLike, onComment, onFollow }: AudioPostProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [isLiked, setIsLiked] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize audio element
  useEffect(() => {
    audioRef.current = new Audio(post.audioUrl);
    audioRef.current.addEventListener('ended', handleAudioEnded);

    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.removeEventListener('ended', handleAudioEnded);
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, [post.audioUrl]);

  const handlePlayPause = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
    } else {
      audioRef.current.play();
      progressIntervalRef.current = setInterval(() => {
        if (audioRef.current) {
          setCurrentTime(audioRef.current.currentTime);
        }
      }, 100);
    }

    setIsPlaying(!isPlaying);
  };

  const handleAudioEnded = () => {
    setIsPlaying(false);
    setCurrentTime(0);
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  };

  const handleLike = () => {
    setIsLiked(!isLiked);
    onLike(post.id);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  const progress = (currentTime / post.duration) * 100;

  return (
    <div className="mb-6">
      <div className="flex items-center mb-3">
        <div className="relative h-10 w-10 rounded-full overflow-hidden mr-3">
          {post.authorPhotoUrl ? (
            <Image
              src={post.authorPhotoUrl}
              alt={post.authorName}
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center text-gray-500">
              {post.authorName.charAt(0)}
            </div>
          )}
        </div>
        <div className="flex-1">
          <div className="flex items-center">
            <p className="font-medium text-sm">{post.authorName}</p>
            {!post.isFollowing && (
              <Button
                variant="ghost"
                size="xs"
                className="ml-2"
                onClick={() => onFollow(post.authorId)}
              >
                Follow
              </Button>
            )}
          </div>
          <p className="text-xs text-gray-500">
            {formatRelativeTime(post.createdAt)}
          </p>
        </div>
      </div>

      {/* Book Cover Style Post */}
      <div
        className="relative aspect-[2/3] w-full max-w-md mx-auto rounded-md overflow-hidden shadow-lg"
        style={{
          backgroundColor: post.coverUrl ? 'transparent' : '#000',
        }}
      >
        {/* Cover Image (if available) */}
        {post.coverUrl && (
          <Image
            src={post.coverUrl}
            alt={post.title}
            fill
            className="object-cover"
          />
        )}

        {/* Overlay for text and controls */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-between p-4">
          {/* Title at top */}
          <div className="text-white text-lg font-bold drop-shadow-md">
            {post.title}
          </div>

          {/* Audio visualization in middle */}
          <div className="flex-1 flex items-center justify-center">
            {!post.coverUrl && post.waveformData && (
              <div className="flex items-center h-20 w-full space-x-1">
                {post.waveformData.map((amplitude, index) => (
                  <div
                    key={`waveform-${index}-${amplitude}`}
                    className="w-1 bg-white/80"
                    style={{
                      height: `${amplitude * 100}%`,
                      opacity: isPlaying ? 1 : 0.6
                    }}
                  />
                ))}
              </div>
            )}
          </div>

          {/* Controls at bottom */}
          <div className="mt-auto">
            {/* Progress bar */}
            <div className="w-full h-1 bg-white/30 rounded-full mb-3">
              <div
                className="h-full bg-white rounded-full"
                style={{ width: `${progress}%` }}
              />
            </div>

            <div className="flex items-center justify-between">
              {/* Play/Pause button */}
              <button
                type="button"
                className="w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition"
                onClick={handlePlayPause}
              >
                {isPlaying ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                )}
              </button>

              {/* Time */}
              <div className="text-white text-xs">
                {formatTime(currentTime)} / {formatTime(post.duration)}
              </div>

              {/* Like and comment buttons */}
              <div className="flex space-x-2">
                <button
                  type="button"
                  className="text-white hover:text-pink-500 transition"
                  onClick={handleLike}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className={`h-5 w-5 ${isLiked ? 'text-pink-500 fill-pink-500' : ''}`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                    />
                  </svg>
                </button>
                <button
                  type="button"
                  className="text-white hover:text-blue-500 transition"
                  onClick={() => onComment(post.id)}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Post stats */}
      <div className="flex items-center justify-between mt-3 text-sm text-gray-500">
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
              />
            </svg>
            {post.likes} likes
          </div>
          <div className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
            {post.comments} comments
          </div>
        </div>
      </div>
    </div>
  );
}
