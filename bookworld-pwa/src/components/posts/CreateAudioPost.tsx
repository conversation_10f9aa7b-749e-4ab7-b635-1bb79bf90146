'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';

interface CreateAudioPostProps {
  onSubmit: (data: {
    title: string;
    audioFile: File;
    coverFile?: File;
    duration: number;
  }) => Promise<void>;
}

export default function CreateAudioPost({ onSubmit }: CreateAudioPostProps) {
  const [title, setTitle] = useState('');
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [coverFile, setCoverFile] = useState<File | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [coverUrl, setCoverUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  
  const MAX_RECORDING_TIME = 30; // 30 seconds max
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
      if (coverUrl) {
        URL.revokeObjectURL(coverUrl);
      }
    };
  }, [audioUrl, coverUrl]);
  
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      
      mediaRecorderRef.current.addEventListener('dataavailable', (event) => {
        audioChunksRef.current.push(event.data);
      });
      
      mediaRecorderRef.current.addEventListener('stop', () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/mp3' });
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
        
        // Convert Blob to File
        const file = new File([audioBlob], `recording-${Date.now()}.mp3`, {
          type: 'audio/mp3',
        });
        setAudioFile(file);
        
        // Reset recording state
        audioChunksRef.current = [];
        setIsRecording(false);
      });
      
      // Start recording
      mediaRecorderRef.current.start();
      setIsRecording(true);
      setRecordingTime(0);
      
      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => {
          if (prev >= MAX_RECORDING_TIME) {
            stopRecording();
            return MAX_RECORDING_TIME;
          }
          return prev + 1;
        });
      }, 1000);
    } catch (err) {
      console.error('Error starting recording:', err);
      setError('Could not access microphone. Please check your browser permissions.');
    }
  };
  
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      
      // Stop all audio tracks
      mediaRecorderRef.current.stream.getTracks().forEach((track) => track.stop());
      
      // Clear timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };
  
  const handleAudioFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Check file type
      if (!file.type.startsWith('audio/')) {
        setError('Please select an audio file.');
        return;
      }
      
      // Check file size (10MB max)
      if (file.size > 10 * 1024 * 1024) {
        setError('Audio file size must be less than 10MB.');
        return;
      }
      
      setAudioFile(file);
      setAudioUrl(URL.createObjectURL(file));
      setError(null);
    }
  };
  
  const handleCoverFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Check file type
      if (!file.type.startsWith('image/')) {
        setError('Please select an image file.');
        return;
      }
      
      // Check file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        setError('Image file size must be less than 5MB.');
        return;
      }
      
      setCoverFile(file);
      setCoverUrl(URL.createObjectURL(file));
      setError(null);
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) {
      setError('Please enter a title for your post.');
      return;
    }
    
    if (!audioFile) {
      setError('Please record or upload an audio file.');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      // Get audio duration
      const audio = new Audio(audioUrl!);
      await new Promise((resolve) => {
        audio.addEventListener('loadedmetadata', resolve);
      });
      
      const duration = audio.duration;
      
      // Check if audio is too long
      if (duration > MAX_RECORDING_TIME) {
        setError(`Audio must be ${MAX_RECORDING_TIME} seconds or less.`);
        setLoading(false);
        return;
      }
      
      await onSubmit({
        title,
        audioFile,
        coverFile: coverFile || undefined,
        duration,
      });
      
      // Reset form
      setTitle('');
      setAudioFile(null);
      setCoverFile(null);
      setAudioUrl(null);
      setCoverUrl(null);
      setLoading(false);
    } catch (err) {
      console.error('Error creating post:', err);
      setError('Failed to create post. Please try again.');
      setLoading(false);
    }
  };
  
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };
  
  return (
    <Card className="p-4 mb-6">
      <h2 className="text-lg font-bold mb-4">Create Audio Post</h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <Input
          label="Title"
          id="title"
          name="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="What's your post about?"
          required
          fullWidth
          className="mb-4"
        />
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Audio
          </label>
          
          {audioUrl ? (
            <div className="mb-4">
              <audio src={audioUrl} controls className="w-full" />
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => {
                  setAudioFile(null);
                  setAudioUrl(null);
                }}
              >
                Remove Audio
              </Button>
            </div>
          ) : (
            <div className="flex flex-col space-y-4">
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant={isRecording ? 'danger' : 'primary'}
                  onClick={isRecording ? stopRecording : startRecording}
                >
                  {isRecording ? 'Stop Recording' : 'Record Audio'}
                </Button>
                
                {isRecording && (
                  <div className="flex items-center">
                    <div className="animate-pulse h-3 w-3 bg-red-500 rounded-full mr-2" />
                    <span className="text-sm">
                      Recording... {formatTime(recordingTime)} / {formatTime(MAX_RECORDING_TIME)}
                    </span>
                  </div>
                )}
              </div>
              
              <div className="text-sm text-gray-500">
                Or upload an audio file (max 30 seconds)
              </div>
              
              <div className="border-2 border-dashed border-gray-300 rounded-md p-4 flex flex-col items-center">
                <svg
                  className="h-8 w-8 text-gray-400 mb-2"
                  stroke="currentColor"
                  fill="none"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4"
                  />
                </svg>
                <label
                  htmlFor="audio-upload"
                  className="relative cursor-pointer bg-white rounded-md font-medium text-black hover:text-gray-700 focus-within:outline-none"
                >
                  <span>Upload a file</span>
                  <input
                    id="audio-upload"
                    name="audio-upload"
                    type="file"
                    className="sr-only"
                    accept="audio/*"
                    onChange={handleAudioFileChange}
                  />
                </label>
                <p className="text-xs text-gray-500 mt-2">
                  MP3, WAV, M4A up to 10MB
                </p>
              </div>
            </div>
          )}
        </div>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Cover Image (Optional)
          </label>
          
          <div className="border-2 border-dashed border-gray-300 rounded-md p-4 flex flex-col items-center">
            {coverUrl ? (
              <div className="text-center">
                <div className="mb-3">
                  <div className="relative aspect-[2/3] w-40 mx-auto">
                    <Image
                      src={coverUrl}
                      alt="Cover preview"
                      fill
                      className="object-cover rounded-md"
                    />
                  </div>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setCoverFile(null);
                    setCoverUrl(null);
                  }}
                >
                  Remove
                </Button>
              </div>
            ) : (
              <>
                <svg
                  className="h-8 w-8 text-gray-400 mb-2"
                  stroke="currentColor"
                  fill="none"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                <label
                  htmlFor="cover-upload"
                  className="relative cursor-pointer bg-white rounded-md font-medium text-black hover:text-gray-700 focus-within:outline-none"
                >
                  <span>Upload a cover image</span>
                  <input
                    id="cover-upload"
                    name="cover-upload"
                    type="file"
                    className="sr-only"
                    accept="image/*"
                    onChange={handleCoverFileChange}
                  />
                </label>
                <p className="text-xs text-gray-500 mt-2">
                  PNG, JPG, GIF up to 5MB
                </p>
              </>
            )}
          </div>
        </div>
        
        <div className="flex justify-end">
          <Button
            type="submit"
            isLoading={loading}
            disabled={!title || !audioFile || loading}
          >
            Post
          </Button>
        </div>
      </form>
    </Card>
  );
}
