'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { ePub, Book, Rendition } from 'epubjs';
import Button from '@/components/ui/Button';
import { updateReadingProgress, addHighlight } from '@/lib/utils/books';

interface EpubReaderProps {
  bookId: string;
  userId: string;
  contentUrl: string;
  initialLocation?: string;
  initialPage?: number;
  totalPages?: number;
}

// Helper function to format time ago
function formatTimeAgo(date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) return `${diffInMinutes} min ago`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours} hours ago`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) return `${diffInDays} days ago`;

  const diffInMonths = Math.floor(diffInDays / 30);
  return `${diffInMonths} months ago`;
}

export default function EpubReader({
  bookId,
  userId,
  contentUrl,
  initialLocation,
  initialPage = 1,
  totalPages = 0,
}: EpubReaderProps) {
  const router = useRouter();
  const viewerRef = useRef<HTMLDivElement>(null);
  const [book, setBook] = useState<Book | null>(null);
  const [rendition, setRendition] = useState<Rendition | null>(null);
  const [currentLocation, setCurrentLocation] = useState<string | null>(initialLocation || null);
  const [currentPage, setCurrentPage] = useState<number>(initialPage);
  const [bookTotalPages, setBookTotalPages] = useState<number>(totalPages);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [fontSize, setFontSize] = useState<number>(100);
  const [theme, setTheme] = useState<'light' | 'sepia' | 'dark'>('light');
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
  const [isHighlighting, setIsHighlighting] = useState<boolean>(false);
  const [selectedText, setSelectedText] = useState<string>('');
  const [highlightNote, setHighlightNote] = useState<string>('');
  const [highlightColor, setHighlightColor] = useState<string>('#FFEB3B');
  const [isSocialOpen, setIsSocialOpen] = useState<boolean>(false);
  const [showCommentForm, setShowCommentForm] = useState<boolean>(false);
  const [commentText, setCommentText] = useState<string>('');
  const [readingStats, setReadingStats] = useState({
    timeReading: '0h 0m',
    pagesRead: 0,
    readingSpeed: '0 pages/hour',
    lastSession: '0m ago'
  });
  const [socialHighlights, setSocialHighlights] = useState([
    {
      id: '1',
      text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      page: 12,
      color: '#FFEB3B',
      user: { name: 'Sarah J.', avatar: '' },
      likes: 5,
      comments: 2,
      createdAt: new Date(Date.now() - 86400000 * 2) // 2 days ago
    },
    {
      id: '2',
      text: 'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
      page: 18,
      color: '#4CAF50',
      user: { name: 'Michael T.', avatar: '' },
      likes: 3,
      comments: 1,
      createdAt: new Date(Date.now() - 86400000 * 5) // 5 days ago
    }
  ]);
  const [communityNotes, setCommunityNotes] = useState([
    {
      id: '1',
      text: 'This passage reminds me of Hemingway\'s style. Very direct and impactful.',
      page: 8,
      user: { name: 'Sarah J.', avatar: '' },
      likes: 7,
      replies: 3,
      createdAt: new Date(Date.now() - 86400000 * 2) // 2 days ago
    },
    {
      id: '2',
      text: 'The author\'s use of metaphor here is brilliant. It connects to the earlier themes.',
      page: 15,
      user: { name: 'Michael T.', avatar: '' },
      likes: 4,
      replies: 0,
      createdAt: new Date(Date.now() - 86400000 * 5) // 5 days ago
    }
  ]);
  const [readingGroups, setReadingGroups] = useState([
    {
      id: '1',
      name: 'Fantasy Book Club',
      members: 128,
      online: 5,
      unreadMessages: 12
    },
    {
      id: '2',
      name: 'Literary Fiction Readers',
      members: 87,
      online: 3,
      unreadMessages: 0
    }
  ]);

  // Initialize the book
  useEffect(() => {
    if (!viewerRef.current) return;

    setIsLoading(true);

    const initBook = async () => {
      try {
        // Create a new book
        const newBook = ePub(contentUrl);
        setBook(newBook);

        // Create a rendition
        const newRendition = newBook.renderTo(viewerRef.current, {
          width: '100%',
          height: '100%',
          spread: 'none',
        });

        setRendition(newRendition);

        // Get the total number of pages
        newBook.ready.then(() => {
          const totalPagesCount = newBook.spine.length;
          setBookTotalPages(totalPagesCount);

          // Display the book
          if (currentLocation) {
            newRendition.display(currentLocation);
          } else {
            newRendition.display();
          }

          setIsLoading(false);
        });

        // Set up event listeners
        newRendition.on('locationChanged', (location) => {
          const loc = location.start.cfi;
          setCurrentLocation(loc);

          // Calculate current page
          const page = newBook.locations.percentageFromCfi(loc);
          const pageNumber = Math.ceil(page * bookTotalPages);
          setCurrentPage(pageNumber);

          // Update reading progress in Firestore
          updateReadingProgress(userId, bookId, {
            currentPage: pageNumber,
            totalPages: bookTotalPages,
            progress: Math.round(page * 100),
          });
        });

        // Handle text selection for highlighting
        newRendition.on('selected', (cfiRange, contents) => {
          if (isHighlighting) {
            const text = contents.window.getSelection()?.toString() || '';
            setSelectedText(text);

            if (text) {
              // Show highlight options
              setIsMenuOpen(false);

              // Apply temporary highlight
              newRendition.annotations.highlight(cfiRange, {}, (e: MouseEvent) => {
                // Handle click on highlight
              });

              // Save the highlight
              addHighlight(userId, bookId, {
                page: currentPage,
                text,
                color: highlightColor,
                note: highlightNote,
              });

              // Reset
              setSelectedText('');
              setHighlightNote('');
            }
          }
        });

      } catch (error) {
        console.error('Error initializing e-reader:', error);
        setIsLoading(false);
      }
    };

    initBook();

    return () => {
      if (book) {
        book.destroy();
      }
    };
  }, [viewerRef, contentUrl]);

  // Handle page navigation
  const goToPreviousPage = () => {
    if (rendition) {
      rendition.prev();
    }
  };

  const goToNextPage = () => {
    if (rendition) {
      rendition.next();
    }
  };

  // Handle font size changes
  const increaseFontSize = () => {
    if (rendition && fontSize < 150) {
      const newSize = fontSize + 10;
      setFontSize(newSize);
      rendition.themes.fontSize(`${newSize}%`);
    }
  };

  const decreaseFontSize = () => {
    if (rendition && fontSize > 70) {
      const newSize = fontSize - 10;
      setFontSize(newSize);
      rendition.themes.fontSize(`${newSize}%`);
    }
  };

  // Handle theme changes
  const changeTheme = (newTheme: 'light' | 'sepia' | 'dark') => {
    if (rendition) {
      setTheme(newTheme);

      if (newTheme === 'light') {
        rendition.themes.override('color', '#000000');
        rendition.themes.override('background', '#ffffff');
      } else if (newTheme === 'sepia') {
        rendition.themes.override('color', '#5b4636');
        rendition.themes.override('background', '#f4ecd8');
      } else if (newTheme === 'dark') {
        rendition.themes.override('color', '#cccccc');
        rendition.themes.override('background', '#222222');
      }
    }
  };

  // Toggle highlighting mode
  const toggleHighlighting = () => {
    setIsHighlighting(!isHighlighting);
    setIsMenuOpen(false);
  };

  return (
    <div className="flex flex-col h-screen">
      {/* Reader header */}
      <div className="flex items-center justify-between p-4 border-b">
        <Button
          variant="ghost"
          onClick={() => router.back()}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
        </Button>

        <div className="text-sm">
          Page {currentPage} of {bookTotalPages}
        </div>

        <div className="flex space-x-2">
          <Button
            variant="ghost"
            onClick={() => setIsSocialOpen(!isSocialOpen)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
            </svg>
          </Button>

          <Button
            variant="ghost"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
          </Button>
        </div>
      </div>

      {/* Reader content */}
      <div className="flex-1 relative overflow-hidden">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
          </div>
        )}

        <div
          ref={viewerRef}
          className="h-full w-full"
          style={{
            backgroundColor: theme === 'light' ? '#ffffff' : theme === 'sepia' ? '#f4ecd8' : '#222222',
          }}
        />

        {/* Page navigation buttons */}
        <div className="absolute inset-y-0 left-0 flex items-center">
          <Button
            variant="ghost"
            className="h-12 w-12 rounded-full bg-white/50 hover:bg-white/80"
            onClick={goToPreviousPage}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </Button>
        </div>

        <div className="absolute inset-y-0 right-0 flex items-center">
          <Button
            variant="ghost"
            className="h-12 w-12 rounded-full bg-white/50 hover:bg-white/80"
            onClick={goToNextPage}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </Button>
        </div>
      </div>

      {/* Social sidebar */}
      {isSocialOpen && (
        <div className="absolute top-16 right-0 bottom-0 w-80 bg-white border-l shadow-lg p-4 transition-transform transform animate-slide-in-right z-10 overflow-auto">
          <div className="flex justify-between mb-4">
            <h3 className="font-medium">Social Reading</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsSocialOpen(false)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </Button>
          </div>

          <div className="mb-6">
            <h4 className="text-sm font-medium mb-2">Reading Stats</h4>
            <div className="bg-gray-100 p-3 rounded-md">
              <div className="flex justify-between mb-1">
                <span className="text-xs text-gray-500">Progress</span>
                <span className="text-xs font-medium">{Math.round((currentPage / bookTotalPages) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                <div
                  className="bg-black rounded-full h-2"
                  style={{ width: `${(currentPage / bookTotalPages) * 100}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-xs text-gray-500">
                <span>Time reading: {readingStats.timeReading}</span>
                <span>Pages today: {readingStats.pagesRead}</span>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-sm font-medium">Community Highlights</h4>
              <Button
                variant="ghost"
                size="xs"
                onClick={() => setIsHighlighting(!isHighlighting)}
              >
                {isHighlighting ? 'Cancel' : 'Add'}
              </Button>
            </div>
            <div className="space-y-3">
              {socialHighlights.map(highlight => (
                <div key={highlight.id} className="border-l-4 p-3 text-xs" style={{ borderColor: highlight.color, backgroundColor: `${highlight.color}20` }}>
                  <p className="mb-1">"{highlight.text}"</p>
                  <div className="flex justify-between items-center mt-2">
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full bg-gray-300 mr-1 flex items-center justify-center text-xs">
                        {highlight.user.name.charAt(0)}
                      </div>
                      <span className="text-gray-600 text-xs">{highlight.user.name}</span>
                    </div>
                    <span className="text-gray-500">Page {highlight.page}</span>
                  </div>
                  <div className="flex items-center mt-2 space-x-4">
                    <button className="text-xs text-gray-500 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z" />
                      </svg>
                      {highlight.likes}
                    </button>
                    <button className="text-xs text-gray-500 flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z" clipRule="evenodd" />
                      </svg>
                      {highlight.comments}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-sm font-medium">Community Notes</h4>
              <Button
                variant="ghost"
                size="xs"
                onClick={() => setShowCommentForm(!showCommentForm)}
              >
                {showCommentForm ? 'Cancel' : 'Add'}
              </Button>
            </div>

            {showCommentForm && (
              <div className="mb-3 p-3 bg-gray-50 rounded-md">
                <textarea
                  className="w-full text-xs p-2 border rounded-md mb-2"
                  rows={3}
                  placeholder="Share your thoughts about this part of the book..."
                  value={commentText}
                  onChange={(e) => setCommentText(e.target.value)}
                ></textarea>
                <div className="flex justify-end">
                  <Button
                    size="xs"
                    onClick={() => {
                      // Add comment logic here
                      setShowCommentForm(false);
                      setCommentText('');
                    }}
                  >
                    Post
                  </Button>
                </div>
              </div>
            )}

            <div className="space-y-3">
              {communityNotes.map(note => (
                <div key={note.id} className="bg-gray-50 p-3 rounded-md text-xs">
                  <div className="flex items-center mb-1">
                    <div className="w-6 h-6 rounded-full bg-gray-300 mr-2 flex items-center justify-center text-xs">
                      {note.user.name.charAt(0)}
                    </div>
                    <span className="font-medium">{note.user.name}</span>
                  </div>
                  <p className="my-2">{note.text}</p>
                  <div className="flex items-center justify-between text-gray-500">
                    <div className="flex items-center space-x-3">
                      <button className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z" />
                        </svg>
                        {note.likes}
                      </button>
                      <button className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z" clipRule="evenodd" />
                        </svg>
                        Reply
                      </button>
                    </div>
                    <div className="text-xs">
                      Page {note.page} • {formatTimeAgo(note.createdAt)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">Reading Groups</h4>
            <div className="space-y-3">
              {readingGroups.map(group => (
                <div key={group.id} className="bg-gray-50 p-3 rounded-md">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-sm">{group.name}</span>
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">{group.online} online</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">{group.members} members</span>
                    {group.unreadMessages > 0 && (
                      <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">{group.unreadMessages} new</span>
                    )}
                  </div>
                  <Button
                    size="sm"
                    fullWidth
                    className="mt-2"
                    onClick={() => {/* Open chat functionality */}}
                  >
                    Join Discussion
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Reader menu */}
      {isMenuOpen && (
        <div className="absolute bottom-0 left-0 right-0 bg-white border-t shadow-lg p-4 transition-transform transform animate-slide-up">
          <div className="flex justify-between mb-4">
            <h3 className="font-medium">Reader Settings</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMenuOpen(false)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </Button>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium mb-2">Font Size</h4>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={decreaseFontSize}
                >
                  A-
                </Button>
                <div className="text-sm">{fontSize}%</div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={increaseFontSize}
                >
                  A+
                </Button>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-2">Theme</h4>
              <div className="flex items-center space-x-2">
                <button
                  className={`w-8 h-8 rounded-full border ${theme === 'light' ? 'ring-2 ring-black' : ''}`}
                  style={{ backgroundColor: '#ffffff' }}
                  onClick={() => changeTheme('light')}
                />
                <button
                  className={`w-8 h-8 rounded-full border ${theme === 'sepia' ? 'ring-2 ring-black' : ''}`}
                  style={{ backgroundColor: '#f4ecd8' }}
                  onClick={() => changeTheme('sepia')}
                />
                <button
                  className={`w-8 h-8 rounded-full border ${theme === 'dark' ? 'ring-2 ring-black' : ''}`}
                  style={{ backgroundColor: '#222222' }}
                  onClick={() => changeTheme('dark')}
                />
              </div>
            </div>
          </div>

          <div className="mt-4">
            <h4 className="text-sm font-medium mb-2">Highlighting</h4>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="highlight-toggle"
                  checked={isHighlighting}
                  onChange={toggleHighlighting}
                  className="rounded text-black focus:ring-black"
                />
                <label htmlFor="highlight-toggle" className="text-sm">
                  Enable highlighting
                </label>
              </div>

              {isHighlighting && (
                <div className="flex items-center space-x-2">
                  <button
                    className={`w-6 h-6 rounded-full ${highlightColor === '#FFEB3B' ? 'ring-2 ring-black' : ''}`}
                    style={{ backgroundColor: '#FFEB3B' }}
                    onClick={() => setHighlightColor('#FFEB3B')}
                  />
                  <button
                    className={`w-6 h-6 rounded-full ${highlightColor === '#4CAF50' ? 'ring-2 ring-black' : ''}`}
                    style={{ backgroundColor: '#4CAF50' }}
                    onClick={() => setHighlightColor('#4CAF50')}
                  />
                  <button
                    className={`w-6 h-6 rounded-full ${highlightColor === '#2196F3' ? 'ring-2 ring-black' : ''}`}
                    style={{ backgroundColor: '#2196F3' }}
                    onClick={() => setHighlightColor('#2196F3')}
                  />
                  <button
                    className={`w-6 h-6 rounded-full ${highlightColor === '#F44336' ? 'ring-2 ring-black' : ''}`}
                    style={{ backgroundColor: '#F44336' }}
                    onClick={() => setHighlightColor('#F44336')}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
