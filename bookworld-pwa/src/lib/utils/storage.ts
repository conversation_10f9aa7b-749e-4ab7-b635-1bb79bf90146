import { storage } from '../firebase/config';
import {
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject,
  listAll,
  UploadResult,
  StorageReference,
} from 'firebase/storage';

// Upload a file to Firebase Storage
export const uploadFile = async (
  path: string,
  file: File | Blob | Uint8Array | ArrayBuffer
): Promise<{ url: string; path: string }> => {
  const storageRef = ref(storage, path);
  const snapshot = await uploadBytes(storageRef, file);
  const url = await getDownloadURL(snapshot.ref);
  
  return {
    url,
    path,
  };
};

// Get download URL for a file
export const getFileUrl = async (path: string): Promise<string> => {
  const storageRef = ref(storage, path);
  return getDownloadURL(storageRef);
};

// Delete a file from Firebase Storage
export const deleteFile = async (path: string): Promise<void> => {
  const storageRef = ref(storage, path);
  await deleteObject(storageRef);
};

// List all files in a directory
export const listFiles = async (path: string): Promise<{ name: string; url: string }[]> => {
  const storageRef = ref(storage, path);
  const res = await listAll(storageRef);
  
  const files = await Promise.all(
    res.items.map(async (itemRef) => {
      const url = await getDownloadURL(itemRef);
      return {
        name: itemRef.name,
        url,
      };
    })
  );
  
  return files;
};
