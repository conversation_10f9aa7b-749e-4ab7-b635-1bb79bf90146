import { db } from '../firebase/config';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { getDocumentById, updateDocument } from './firestore';
import type { UserProfile } from '../types/user';

export interface AuthorProfile {
  id: string;
  name: string;
  bio: string;
  photoUrl: string;
  website: string;
  socialLinks: {
    [key: string]: string;
  };
  followerCount: number;
  bookMailSubscriberCount: number;
}

/**
 * Get an author's profile by ID
 */
export async function getAuthorProfile(authorId: string): Promise<AuthorProfile | null> {
  try {
    // Get the user document from Firestore
    const userDoc = await getDocumentById<UserProfile>('users', authorId);
    
    if (!userDoc) {
      return null;
    }
    
    // Convert the user document to an author profile
    const authorProfile: AuthorProfile = {
      id: userDoc.uid,
      name: userDoc.displayName || 'Unknown Author',
      bio: userDoc.bio || '',
      photoUrl: userDoc.photoURL || '',
      website: '', // Not in UserProfile type
      socialLinks: {}, // Not in UserProfile type
      followerCount: userDoc.followers?.length || 0,
      bookMailSubscriberCount: 0 // Not in UserProfile type
    };
    
    return authorProfile;
  } catch (error) {
    console.error('Error getting author profile:', error);
    return null;
  }
}

/**
 * Update an author's bio
 */
export async function updateAuthorBio(authorId: string, bio: string): Promise<boolean> {
  try {
    // Get the current user document first
    const userDoc = await getDocumentById<UserProfile>('users', authorId);
    if (!userDoc) {
      console.error('User document not found');
      return false;
    }

    // Update the bio field
    await updateDocument('users', authorId, { 
      bio,
      updatedAt: new Date() // Add timestamp for tracking
    });

    return true;
  } catch (error) {
    console.error('Error updating author bio:', error);
    return false;
  }
}

/**
 * Follow an author
 */
export async function followAuthor(userId: string, authorId: string): Promise<boolean> {
  try {
    // Get the current user document
    const userDocRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userDocRef);
    
    if (!userDoc.exists()) {
      return false;
    }
    
    const userData = userDoc.data();
    const following = userData.following || [];
    
    // Check if already following
    if (following.includes(authorId)) {
      return true;
    }
    
    // Add the author to the user's following list
    await updateDoc(userDocRef, {
      following: [...following, authorId]
    });
    
    // Get the author document
    const authorDocRef = doc(db, 'users', authorId);
    const authorDoc = await getDoc(authorDocRef);
    
    if (!authorDoc.exists()) {
      return false;
    }
    
    const authorData = authorDoc.data();
    const followers = authorData.followers || [];
    
    // Add the user to the author's followers list
    await updateDoc(authorDocRef, {
      followers: [...followers, userId]
    });
    
    return true;
  } catch (error) {
    console.error('Error following author:', error);
    return false;
  }
}

/**
 * Subscribe to an author's BookMail
 */
export async function subscribeToBookMail(userId: string, authorId: string): Promise<boolean> {
  try {
    // Get the author document
    const authorDocRef = doc(db, 'users', authorId);
    const authorDoc = await getDoc(authorDocRef);
    
    if (!authorDoc.exists()) {
      return false;
    }
    
    const authorData = authorDoc.data();
    const bookMailSubscribers = authorData.bookMailSubscribers || [];
    
    // Check if already subscribed
    if (bookMailSubscribers.includes(userId)) {
      return true;
    }
    
    // Add the user to the author's BookMail subscribers list
    await updateDoc(authorDocRef, {
      bookMailSubscribers: [...bookMailSubscribers, userId]
    });
    
    // Also add a record in the bookMailSubscriptions collection
    await updateDocument('bookMailSubscriptions', `${userId}_${authorId}`, {
      userId,
      authorId,
      subscribedAt: new Date(),
      active: true
    });
    
    return true;
  } catch (error) {
    console.error('Error subscribing to BookMail:', error);
    return false;
  }
}
