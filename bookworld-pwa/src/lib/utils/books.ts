import { db, storage } from '../firebase/config';
import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit,
  addDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp,
  increment,
  Timestamp
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { BookData } from '@/components/books/BookCard';

// Types
export interface BookDetails extends Omit<BookData, 'publishedAt'> {
  publishedAt: Timestamp;
  content?: string;
  sample?: string;
  pageCount?: number;
  wordCount?: number;
  isbn?: string;
  language?: string;
  categories?: string[];
  isFeatured?: boolean;
  isPublished?: boolean;
}

export interface BookPricing {
  bookId: string;
  price: number;
  currency: string;
  isOnSale?: boolean;
  salePrice?: number;
  saleEndsAt?: Timestamp;
}

export interface Purchase {
  id: string;
  userId: string;
  bookId: string;
  authorId: string;
  price: number;
  currency: string;
  purchasedAt: Timestamp;
  transactionId?: string;
}

export interface ReadingProgress {
  userId: string;
  bookId: string;
  currentPage: number;
  totalPages: number;
  progress: number; // 0-100
  lastReadAt: Timestamp;
  bookmarks?: number[];
  highlights?: {
    id: string;
    page: number;
    text: string;
    color: string;
    note?: string;
    createdAt: Timestamp;
  }[];
}

// Get a book by ID
export async function getBookById(bookId: string): Promise<BookDetails | null> {
  try {
    const bookDoc = await getDoc(doc(db, 'books', bookId));
    
    if (!bookDoc.exists()) {
      return null;
    }
    
    const bookData = bookDoc.data() as BookDetails;
    
    // Get author data
    const authorDoc = await getDoc(doc(db, 'users', bookData.author.id));
    const authorData = authorDoc.data();
    
    // Get pricing data
    const pricingDoc = await getDoc(doc(db, 'bookPricing', bookId));
    const pricingData = pricingDoc.exists() ? pricingDoc.data() as BookPricing : null;
    
    return {
      ...bookData,
      id: bookDoc.id,
      price: pricingData?.price || 0,
      author: {
        ...bookData.author,
        name: authorData?.name || 'Unknown Author',
        photoUrl: authorData?.photoUrl,
      },
    };
  } catch (error) {
    console.error('Error getting book:', error);
    return null;
  }
}

// Get featured books
export async function getFeaturedBooks(limit = 10): Promise<BookData[]> {
  try {
    const booksQuery = query(
      collection(db, 'books'),
      where('isPublished', '==', true),
      where('isFeatured', '==', true),
      orderBy('publishedAt', 'desc'),
      limit
    );
    
    const booksSnapshot = await getDocs(booksQuery);
    const books: BookData[] = [];
    
    for (const bookDoc of booksSnapshot.docs) {
      const bookData = bookDoc.data() as BookDetails;
      
      // Get pricing data
      const pricingDoc = await getDoc(doc(db, 'bookPricing', bookDoc.id));
      const pricingData = pricingDoc.exists() ? pricingDoc.data() as BookPricing : null;
      
      // Get author data
      const authorDoc = await getDoc(doc(db, 'users', bookData.author.id));
      const authorData = authorDoc.data();
      
      books.push({
        ...bookData,
        id: bookDoc.id,
        price: pricingData?.price || 0,
        publishedAt: bookData.publishedAt.toDate(),
        author: {
          ...bookData.author,
          name: authorData?.name || 'Unknown Author',
          photoUrl: authorData?.photoUrl,
        },
      });
    }
    
    return books;
  } catch (error) {
    console.error('Error getting featured books:', error);
    return [];
  }
}

// Get latest books
export async function getLatestBooks(limit = 10): Promise<BookData[]> {
  try {
    const booksQuery = query(
      collection(db, 'books'),
      where('isPublished', '==', true),
      orderBy('publishedAt', 'desc'),
      limit
    );
    
    const booksSnapshot = await getDocs(booksQuery);
    const books: BookData[] = [];
    
    for (const bookDoc of booksSnapshot.docs) {
      const bookData = bookDoc.data() as BookDetails;
      
      // Get pricing data
      const pricingDoc = await getDoc(doc(db, 'bookPricing', bookDoc.id));
      const pricingData = pricingDoc.exists() ? pricingDoc.data() as BookPricing : null;
      
      // Get author data
      const authorDoc = await getDoc(doc(db, 'users', bookData.author.id));
      const authorData = authorDoc.data();
      
      books.push({
        ...bookData,
        id: bookDoc.id,
        price: pricingData?.price || 0,
        publishedAt: bookData.publishedAt.toDate(),
        author: {
          ...bookData.author,
          name: authorData?.name || 'Unknown Author',
          photoUrl: authorData?.photoUrl,
        },
      });
    }
    
    return books;
  } catch (error) {
    console.error('Error getting latest books:', error);
    return [];
  }
}

// Get books by author
export async function getBooksByAuthor(authorId: string, limit = 10): Promise<BookData[]> {
  try {
    const booksQuery = query(
      collection(db, 'books'),
      where('author.id', '==', authorId),
      where('isPublished', '==', true),
      orderBy('publishedAt', 'desc'),
      limit
    );
    
    const booksSnapshot = await getDocs(booksQuery);
    const books: BookData[] = [];
    
    for (const bookDoc of booksSnapshot.docs) {
      const bookData = bookDoc.data() as BookDetails;
      
      // Get pricing data
      const pricingDoc = await getDoc(doc(db, 'bookPricing', bookDoc.id));
      const pricingData = pricingDoc.exists() ? pricingDoc.data() as BookPricing : null;
      
      // Get author data
      const authorDoc = await getDoc(doc(db, 'users', bookData.author.id));
      const authorData = authorDoc.data();
      
      books.push({
        ...bookData,
        id: bookDoc.id,
        price: pricingData?.price || 0,
        publishedAt: bookData.publishedAt.toDate(),
        author: {
          ...bookData.author,
          name: authorData?.name || 'Unknown Author',
          photoUrl: authorData?.photoUrl,
        },
      });
    }
    
    return books;
  } catch (error) {
    console.error('Error getting books by author:', error);
    return [];
  }
}

// Check if user has purchased a book
export async function hasUserPurchasedBook(userId: string, bookId: string): Promise<boolean> {
  try {
    const libraryItemId = `${userId}_${bookId}`;
    const libraryItemDoc = await getDoc(doc(db, 'libraryItems', libraryItemId));
    
    return libraryItemDoc.exists();
  } catch (error) {
    console.error('Error checking if user purchased book:', error);
    return false;
  }
}

// Get user's library (purchased books)
export async function getUserLibrary(userId: string): Promise<BookData[]> {
  try {
    const libraryQuery = query(
      collection(db, 'libraryItems'),
      where('userId', '==', userId),
      orderBy('purchasedAt', 'desc')
    );
    
    const librarySnapshot = await getDocs(libraryQuery);
    const books: BookData[] = [];
    
    for (const itemDoc of librarySnapshot.docs) {
      const itemData = itemDoc.data();
      const bookId = itemData.bookId;
      
      // Get book data
      const bookDoc = await getDoc(doc(db, 'books', bookId));
      
      if (bookDoc.exists()) {
        const bookData = bookDoc.data() as BookDetails;
        
        // Get author data
        const authorDoc = await getDoc(doc(db, 'users', bookData.author.id));
        const authorData = authorDoc.data();
        
        books.push({
          ...bookData,
          id: bookDoc.id,
          price: itemData.price || 0,
          publishedAt: bookData.publishedAt.toDate(),
          author: {
            ...bookData.author,
            name: authorData?.name || 'Unknown Author',
            photoUrl: authorData?.photoUrl,
          },
        });
      }
    }
    
    return books;
  } catch (error) {
    console.error('Error getting user library:', error);
    return [];
  }
}

// Get reading progress for a book
export async function getReadingProgress(userId: string, bookId: string): Promise<ReadingProgress | null> {
  try {
    const progressId = `${userId}_${bookId}`;
    const progressDoc = await getDoc(doc(db, 'readingProgress', progressId));
    
    if (!progressDoc.exists()) {
      return null;
    }
    
    return progressDoc.data() as ReadingProgress;
  } catch (error) {
    console.error('Error getting reading progress:', error);
    return null;
  }
}

// Update reading progress
export async function updateReadingProgress(
  userId: string,
  bookId: string,
  progress: Partial<ReadingProgress>
): Promise<void> {
  try {
    const progressId = `${userId}_${bookId}`;
    const progressRef = doc(db, 'readingProgress', progressId);
    const progressDoc = await getDoc(progressRef);
    
    if (progressDoc.exists()) {
      await updateDoc(progressRef, {
        ...progress,
        lastReadAt: serverTimestamp(),
      });
    } else {
      await updateDoc(progressRef, {
        userId,
        bookId,
        currentPage: progress.currentPage || 1,
        totalPages: progress.totalPages || 1,
        progress: progress.progress || 0,
        lastReadAt: serverTimestamp(),
        bookmarks: progress.bookmarks || [],
        highlights: progress.highlights || [],
      });
    }
  } catch (error) {
    console.error('Error updating reading progress:', error);
    throw error;
  }
}

// Add a highlight to a book
export async function addHighlight(
  userId: string,
  bookId: string,
  highlight: {
    page: number;
    text: string;
    color: string;
    note?: string;
  }
): Promise<string> {
  try {
    const progressId = `${userId}_${bookId}`;
    const progressRef = doc(db, 'readingProgress', progressId);
    const progressDoc = await getDoc(progressRef);
    
    const highlightId = crypto.randomUUID();
    const newHighlight = {
      id: highlightId,
      ...highlight,
      createdAt: serverTimestamp(),
    };
    
    if (progressDoc.exists()) {
      const progressData = progressDoc.data() as ReadingProgress;
      const highlights = progressData.highlights || [];
      
      await updateDoc(progressRef, {
        highlights: [...highlights, newHighlight],
      });
    } else {
      await updateDoc(progressRef, {
        userId,
        bookId,
        currentPage: highlight.page,
        totalPages: 1,
        progress: 0,
        lastReadAt: serverTimestamp(),
        bookmarks: [],
        highlights: [newHighlight],
      });
    }
    
    return highlightId;
  } catch (error) {
    console.error('Error adding highlight:', error);
    throw error;
  }
}

// Upload a book cover
export async function uploadBookCover(
  authorId: string,
  bookId: string,
  file: File
): Promise<string> {
  try {
    const coverRef = ref(storage, `books/${authorId}/${bookId}/cover/${file.name}`);
    await uploadBytes(coverRef, file);
    
    const downloadUrl = await getDownloadURL(coverRef);
    
    // Update the book document with the cover URL
    await updateDoc(doc(db, 'books', bookId), {
      coverUrl: downloadUrl,
    });
    
    return downloadUrl;
  } catch (error) {
    console.error('Error uploading book cover:', error);
    throw error;
  }
}

// Upload book content
export async function uploadBookContent(
  authorId: string,
  bookId: string,
  file: File
): Promise<string> {
  try {
    const contentRef = ref(storage, `books/${authorId}/${bookId}/content/${file.name}`);
    await uploadBytes(contentRef, file);
    
    const downloadUrl = await getDownloadURL(contentRef);
    
    // Update the book document with the content URL
    await updateDoc(doc(db, 'books', bookId), {
      contentUrl: downloadUrl,
    });
    
    return downloadUrl;
  } catch (error) {
    console.error('Error uploading book content:', error);
    throw error;
  }
}

// Create a new book
export async function createBook(
  authorId: string,
  bookData: Omit<BookDetails, 'id' | 'author' | 'publishedAt'>
): Promise<string> {
  try {
    const bookRef = await addDoc(collection(db, 'books'), {
      ...bookData,
      author: {
        id: authorId,
      },
      isPublished: false,
      isFeatured: false,
      publishedAt: serverTimestamp(),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
    
    return bookRef.id;
  } catch (error) {
    console.error('Error creating book:', error);
    throw error;
  }
}

// Update book pricing
export async function updateBookPricing(
  bookId: string,
  pricing: Omit<BookPricing, 'bookId'>
): Promise<void> {
  try {
    const pricingRef = doc(db, 'bookPricing', bookId);
    const pricingDoc = await getDoc(pricingRef);
    
    if (pricingDoc.exists()) {
      await updateDoc(pricingRef, {
        ...pricing,
        updatedAt: serverTimestamp(),
      });
    } else {
      await updateDoc(pricingRef, {
        bookId,
        ...pricing,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
    }
  } catch (error) {
    console.error('Error updating book pricing:', error);
    throw error;
  }
}

// Publish a book
export async function publishBook(bookId: string): Promise<void> {
  try {
    await updateDoc(doc(db, 'books', bookId), {
      isPublished: true,
      publishedAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error publishing book:', error);
    throw error;
  }
}
