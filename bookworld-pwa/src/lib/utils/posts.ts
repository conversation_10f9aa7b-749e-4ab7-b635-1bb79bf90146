import { db, storage } from '@/lib/firebase/config';
import { 
  collection, 
  addDoc, 
  updateDoc, 
  doc, 
  increment, 
  arrayUnion, 
  arrayRemove, 
  getDoc, 
  setDoc, 
  serverTimestamp 
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';

interface CreateAudioPostParams {
  userId: string;
  userName: string;
  userPhotoUrl?: string;
  title: string;
  audioFile: File;
  coverFile?: File;
  duration: number;
  waveformData?: number[];
}

export const createAudioPost = async ({
  userId,
  userName,
  userPhotoUrl,
  title,
  audioFile,
  coverFile,
  duration,
  waveformData,
}: CreateAudioPostParams) => {
  try {
    // 1. Upload audio file to Firebase Storage
    const audioFileRef = ref(storage, `posts/${userId}/${Date.now()}_audio.mp3`);
    await uploadBytes(audioFileRef, audioFile);
    const audioUrl = await getDownloadURL(audioFileRef);
    
    // 2. Upload cover image if provided
    let coverUrl = '';
    if (coverFile) {
      const coverFileRef = ref(storage, `posts/${userId}/${Date.now()}_cover.jpg`);
      await uploadBytes(coverFileRef, coverFile);
      coverUrl = await getDownloadURL(coverFileRef);
    }
    
    // 3. Create post document in Firestore
    const postData = {
      authorId: userId,
      authorName: userName,
      authorPhotoUrl: userPhotoUrl || null,
      title,
      audioUrl,
      coverUrl: coverUrl || null,
      duration,
      waveformData: waveformData || [],
      likes: 0,
      comments: 0,
      createdAt: serverTimestamp(),
    };
    
    const postRef = await addDoc(collection(db, 'posts'), postData);
    
    return postRef.id;
  } catch (error) {
    console.error('Error creating audio post:', error);
    throw error;
  }
};

export const likePost = async (postId: string, userId: string) => {
  try {
    const postRef = doc(db, 'posts', postId);
    const postDoc = await getDoc(postRef);
    
    if (!postDoc.exists()) {
      throw new Error('Post not found');
    }
    
    const likeRef = doc(db, 'posts', postId, 'likes', userId);
    const likeDoc = await getDoc(likeRef);
    
    if (likeDoc.exists()) {
      // User already liked the post, so unlike it
      await setDoc(likeRef, { userId, createdAt: serverTimestamp() }, { merge: true });
      await updateDoc(postRef, {
        likes: increment(-1),
      });
    } else {
      // User hasn't liked the post yet, so like it
      await setDoc(likeRef, { userId, createdAt: serverTimestamp() });
      await updateDoc(postRef, {
        likes: increment(1),
      });
    }
  } catch (error) {
    console.error('Error liking post:', error);
    throw error;
  }
};

export const followUser = async (currentUserId: string, targetUserId: string) => {
  try {
    // Update current user's following list
    const currentUserRef = doc(db, 'users', currentUserId);
    await updateDoc(currentUserRef, {
      following: arrayUnion(targetUserId),
    });
    
    // Update target user's followers list
    const targetUserRef = doc(db, 'users', targetUserId);
    await updateDoc(targetUserRef, {
      followers: arrayUnion(currentUserId),
    });
  } catch (error) {
    console.error('Error following user:', error);
    throw error;
  }
};

export const unfollowUser = async (currentUserId: string, targetUserId: string) => {
  try {
    // Update current user's following list
    const currentUserRef = doc(db, 'users', currentUserId);
    await updateDoc(currentUserRef, {
      following: arrayRemove(targetUserId),
    });
    
    // Update target user's followers list
    const targetUserRef = doc(db, 'users', targetUserId);
    await updateDoc(targetUserRef, {
      followers: arrayRemove(currentUserId),
    });
  } catch (error) {
    console.error('Error unfollowing user:', error);
    throw error;
  }
};

export const addComment = async (postId: string, userId: string, userName: string, userPhotoUrl: string | null, content: string) => {
  try {
    const commentData = {
      userId,
      userName,
      userPhotoUrl,
      content,
      createdAt: serverTimestamp(),
    };
    
    // Add comment to comments subcollection
    const commentRef = await addDoc(collection(db, 'posts', postId, 'comments'), commentData);
    
    // Increment comment count on post
    const postRef = doc(db, 'posts', postId);
    await updateDoc(postRef, {
      comments: increment(1),
    });
    
    return commentRef.id;
  } catch (error) {
    console.error('Error adding comment:', error);
    throw error;
  }
};
