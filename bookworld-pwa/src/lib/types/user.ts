import type { User as FirebaseUser } from 'firebase/auth';

export interface BookWorldUser extends FirebaseUser {
  role?: 'reader' | 'author';
  following?: string[];
  followers?: string[];
  bio?: string;
  profilePictureUrl?: string;
  createdAt?: string;
  name?: string;
}

export interface UserProfile {
  uid: string;
  displayName: string | null;
  photoURL: string | null;
  email: string | null;
  role: 'reader' | 'author';
  following: string[];
  followers: string[];
  bio: string;
  createdAt: Date;
  profilePictureUrl?: string;
}
