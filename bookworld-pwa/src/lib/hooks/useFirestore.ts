import { useState, useEffect } from 'react';
import {
  collection,
  doc,
  onSnapshot,
  query,
  where,
  orderBy,
  limit,
  DocumentData,
  Query,
  QueryConstraint,
} from 'firebase/firestore';
import { db } from '../firebase/config';

// Hook for real-time document updates
export function useDocument<T = DocumentData>(
  collectionName: string,
  docId: string | null | undefined
) {
  const [document, setDocument] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!docId) {
      setDocument(null);
      setLoading(false);
      return;
    }

    const docRef = doc(db, collectionName, docId);
    const unsubscribe = onSnapshot(
      docRef,
      (snapshot) => {
        if (snapshot.exists()) {
          setDocument({ id: snapshot.id, ...snapshot.data() } as T);
        } else {
          setDocument(null);
        }
        setLoading(false);
        setError(null);
      },
      (err) => {
        console.error('Error fetching document:', err);
        setError(err.message);
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [collectionName, docId]);

  return { document, loading, error };
}

// Hook for real-time collection updates
export function useCollection<T = DocumentData>(
  collectionName: string,
  queryConstraints: QueryConstraint[] = []
) {
  const [documents, setDocuments] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let collectionRef = collection(db, collectionName);
    let queryRef = query(collectionRef, ...queryConstraints);

    const unsubscribe = onSnapshot(
      queryRef,
      (snapshot) => {
        const results: T[] = [];
        snapshot.forEach((doc) => {
          results.push({ id: doc.id, ...doc.data() } as T);
        });
        setDocuments(results);
        setLoading(false);
        setError(null);
      },
      (err) => {
        console.error('Error fetching collection:', err);
        setError(err.message);
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [collectionName, JSON.stringify(queryConstraints)]);

  return { documents, loading, error };
}

// Helper function to create query constraints
export function createQueryConstraints(
  conditions: Array<{
    field: string;
    operator: '==' | '!=' | '>' | '>=' | '<' | '<=';
    value: any;
  }> = [],
  sortField?: string,
  sortDirection?: 'asc' | 'desc',
  limitCount?: number
): QueryConstraint[] {
  const constraints: QueryConstraint[] = [];

  // Add where conditions
  conditions.forEach((condition) => {
    constraints.push(where(condition.field, condition.operator, condition.value));
  });

  // Add orderBy if specified
  if (sortField) {
    constraints.push(orderBy(sortField, sortDirection || 'asc'));
  }

  // Add limit if specified
  if (limitCount) {
    constraints.push(limit(limitCount));
  }

  return constraints;
}
