'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/lib/context/AuthContext';
import ProfilePicture from '@/components/profile/ProfilePicture';
import ProfilePictureSelector from '@/components/profile/ProfilePictureSelector';

export default function ProfilePage() {
  const { user, loading } = useAuthContext();
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [userBooks, setUserBooks] = useState<Array<{ id: string; title: string; coverUrl: string }>>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    // Redirect if not logged in
    if (!loading && !user) {
      router.push('/signin');
      return;
    }
    
    // Fetch user books if user is an author
    if (user && user.role === 'author') {
      fetchUserBooks();
    } else {
      setIsLoading(false);
    }
  }, [user, loading, router]);
  
  const fetchUserBooks = async () => {
    try {
      // This would be replaced with an actual API call
      // For now, we'll simulate a delay and return mock data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data for demonstration
      setUserBooks([
        { id: '1', title: 'My First Novel', coverUrl: 'https://via.placeholder.com/160x256/3949AB/FFFFFF?text=Novel+1' },
        { id: '2', title: 'The Sequel', coverUrl: 'https://via.placeholder.com/160x256/D81B60/FFFFFF?text=Novel+2' },
      ]);
    } catch (error) {
      console.error('Error fetching user books:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleSaveProfilePicture = async (imageFile: File | string) => {
    if (!user) return;
    
    try {
      const formData = new FormData();
      
      if (typeof imageFile === 'string') {
        // It's a book cover URL
        formData.append('bookCoverUrl', imageFile);
      } else {
        // It's a file upload
        formData.append('file', imageFile);
        // We would also append crop settings here
        formData.append('xOffset', '0');
        formData.append('yOffset', '0');
        formData.append('scale', '1');
      }
      
      const response = await fetch('/api/profile/upload-picture', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload profile picture');
      }
      
      const data = await response.json();
      
      // Update user context with new profile picture
      // This would typically be handled by refreshing the user data
      
      // Close the editor
      setIsEditing(false);
      
      // Force a refresh to show the updated profile picture
      router.refresh();
      
    } catch (error) {
      console.error('Error saving profile picture:', error);
      alert('Failed to save profile picture. Please try again.');
    }
  };
  
  if (loading || isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-600" />
      </div>
    );
  }
  
  if (!user) {
    return null; // Will redirect in useEffect
  }
  
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">Your Profile</h1>
        
        {isEditing ? (
          <ProfilePictureSelector
            currentImageUrl={user.profilePictureUrl}
            userBooks={userBooks}
            onSave={handleSaveProfilePicture}
          />
        ) : (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
              <div className="flex flex-col items-center">
                <ProfilePicture
                  src={user.profilePictureUrl}
                  alt={`${user.name}'s profile`}
                  size="xl"
                />
                <button
                  onClick={() => setIsEditing(true)}
                  className="mt-4 text-purple-600 hover:text-purple-800 font-medium"
                >
                  Change Profile Picture
                </button>
              </div>
              
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">{user.name}</h2>
                <p className="text-gray-600 mb-4">{user.role === 'author' ? 'Author' : 'Reader'}</p>
                
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-700 mb-1">Email</h3>
                    <p className="text-gray-600">{user.email}</p>
                  </div>
                  
                  {user.role === 'author' && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-700 mb-1">Published Books</h3>
                      <p className="text-gray-600">{userBooks.length} books</p>
                    </div>
                  )}
                  
                  <div>
                    <h3 className="text-lg font-medium text-gray-700 mb-1">Member Since</h3>
                    <p className="text-gray-600">{new Date(user.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
