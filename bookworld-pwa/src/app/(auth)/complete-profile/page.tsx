'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/lib/context/AuthContext';
import { updateProfile } from 'firebase/auth';
import { doc, updateDoc, getDoc, setDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { db, storage } from '@/lib/firebase/config';
import { auth } from '@/lib/firebase/auth';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import ProfilePictureSelector from '@/components/profile/ProfilePictureSelector';
import ProfilePicture from '@/components/profile/ProfilePicture';

export default function CompleteProfilePage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuthContext();
  
  const [displayName, setDisplayName] = useState('');
  const [bio, setBio] = useState('');
  const [profilePictureUrl, setProfilePictureUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userBooks, setUserBooks] = useState<Array<{ id: string; title: string; coverUrl: string }>>([]);
  
  useEffect(() => {
    if (!authLoading) {
      if (!user) {
        router.push('/signin');
      } else if (user.photoURL) {
        // User already has a profile picture, redirect to home
        router.push('/home');
      }
      
      if (user?.displayName) {
        setDisplayName(user.displayName);
      }
      
      // Check if user is properly authenticated
      const currentUser = auth.currentUser;
      if (!currentUser) {
        console.error('User is not properly authenticated');
        setError('Authentication error. Please try signing in again.');
      }
      
      // Check if user document exists in Firestore
      const checkUserDocument = async () => {
        try {
          if (!user) return;
          
          const userRef = doc(db, 'users', user.uid);
          const userDoc = await getDoc(userRef);
          
          if (!userDoc.exists()) {
            console.log('User document does not exist in Firestore');
            // We'll create it when the user completes their profile
          } else {
            console.log('User document exists in Firestore');
            // If the user has a bio, set it
            const userData = userDoc.data();
            if (userData.bio) {
              setBio(userData.bio);
            }
            
            // Check if user is an author and fetch their books
            if (userData.role === 'author') {
              fetchUserBooks();
            }
          }
        } catch (error) {
          console.error('Error checking user document:', error);
        }
      };
      
      if (user) {
        checkUserDocument();
      }
    }
  }, [user, authLoading, router]);
  
  const fetchUserBooks = async () => {
    try {
      // This would be replaced with an actual API call
      // For now, we'll simulate a delay and return mock data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data for demonstration
      setUserBooks([
        { id: '1', title: 'My First Novel', coverUrl: 'https://via.placeholder.com/160x256/3949AB/FFFFFF?text=Novel+1' },
        { id: '2', title: 'The Sequel', coverUrl: 'https://via.placeholder.com/160x256/D81B60/FFFFFF?text=Novel+2' },
      ]);
    } catch (error) {
      console.error('Error fetching user books:', error);
    }
  };
  
  const handleProfilePictureChange = async (imageFile: File | string) => {
    try {
      if (!user) {
        console.error('No user found when trying to change profile picture');
        setError('You must be logged in to change your profile picture.');
        return;
      }
      
      console.log('Starting profile picture change process...');
      console.log('Image type:', typeof imageFile);
      
      setLoading(true);
      setError(null);
      
      let photoURL: string;
      
      if (typeof imageFile === 'string') {
        // It's a book cover URL
        console.log('Using book cover URL:', imageFile);
        photoURL = imageFile;
        // Immediately set the profile picture URL for book covers
        setProfilePictureUrl(photoURL);
        console.log('Profile picture URL set to book cover URL:', photoURL);
      } else {
        // It's a file upload
        console.log('Uploading file to Firebase Storage...');
        console.log('File size:', imageFile.size, 'bytes');
        console.log('File type:', imageFile.type);
        
        // Create a timeout promise to prevent hanging
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Upload timed out after 30 seconds')), 30000);
        });
        
        try {
          const profilePictureRef = ref(storage, `users/${user.uid}/profile.jpg`);
          console.log('Storage reference created:', profilePictureRef.fullPath);
          
          console.log('Uploading bytes...');
          // Race the upload against the timeout
          await Promise.race([
            uploadBytes(profilePictureRef, imageFile),
            timeoutPromise
          ]);
          console.log('Bytes uploaded successfully');
          
          console.log('Getting download URL...');
          // Race the download URL fetch against the timeout
          photoURL = await Promise.race([
            getDownloadURL(profilePictureRef),
            timeoutPromise
          ]) as string;
          console.log('Download URL obtained:', photoURL);
          
          // Set the profile picture URL after successful upload
          setProfilePictureUrl(photoURL);
          console.log('Profile picture URL set to uploaded file URL:', photoURL);
        } catch (uploadError) {
          console.error('Error during file upload:', uploadError);
          throw new Error(`Failed to upload file: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`);
        }
      }
      
      // Verify that the profile picture URL was set
      if (!photoURL) {
        throw new Error('Failed to set profile picture URL');
      }
      
      console.log('Profile picture change completed successfully');
      console.log('Final profile picture URL:', photoURL);
      
      // Double-check that the state was updated correctly
      setTimeout(() => {
        console.log('Current profile picture URL state:', profilePictureUrl);
        if (!profilePictureUrl) {
          console.warn('Profile picture URL state is still null after setting it. Setting it again.');
          setProfilePictureUrl(photoURL);
        }
      }, 100);
      
      setLoading(false);
    } catch (err) {
      console.error('Error handling profile picture:', err);
      setError(`Failed to process profile picture: ${err instanceof Error ? err.message : 'Unknown error'}`);
      setLoading(false);
      // Clear the profile picture URL on error
      setProfilePictureUrl('');
    }
  };
  
  // Direct handler for the Complete Profile button
  const handleCompleteProfileClick = () => {
    console.log('Complete Profile button clicked directly');
    if (!user) {
      setError('You must be logged in to complete your profile.');
      return;
    }
    
    if (!displayName.trim()) {
      setError('Please enter a display name.');
      return;
    }
    
    if (!profilePictureUrl) {
      setError('Please select a profile picture. This is required to use BookWorld.');
      return;
    }
    
    // Submit the form programmatically
    const form = document.querySelector('form');
    if (form) {
      form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
    } else {
      console.error('Form not found');
      setError('An error occurred. Please try again.');
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submission started');
    console.log('Current profile picture URL:', profilePictureUrl);
    
    if (!user) {
      console.error('No user found during form submission');
      setError('You must be logged in to complete your profile.');
      return;
    }
    
    if (!displayName.trim()) {
      console.error('Display name is empty');
      setError('Please enter a display name.');
      return;
    }
    
    if (!profilePictureUrl) {
      console.error('Profile picture URL is missing');
      setError('Please select a profile picture. This is required to use BookWorld.');
      return;
    }

    // Verify that the profile picture URL is valid
    try {
      new URL(profilePictureUrl);
    } catch (error) {
      console.error('Invalid profile picture URL:', error);
      setError('The selected profile picture is invalid. Please try selecting it again.');
      return;
    }
    
    try {
      console.log('Starting profile update process...');
      console.log('User ID:', user.uid);
      console.log('Display Name:', displayName);
      console.log('Profile Picture URL:', profilePictureUrl);
      
      setLoading(true);
      setError(null);
      
      // Update user profile in Firebase Auth
      console.log('Updating Firebase Auth profile...');
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('No authenticated user found');
      }
      
      try {
        await updateProfile(currentUser, {
          displayName,
          photoURL: profilePictureUrl,
        });
        console.log('Firebase Auth profile updated successfully');
      } catch (authError) {
        console.error('Error updating Firebase Auth profile:', authError);
        throw new Error(`Failed to update Firebase Auth profile: ${authError instanceof Error ? authError.message : 'Unknown error'}`);
      }
      
      // Check if user document exists in Firestore
      console.log('Checking if user document exists in Firestore...');
      const userRef = doc(db, 'users', user.uid);
      
      try {
        const userDoc = await getDoc(userRef);
        
        if (!userDoc.exists()) {
          console.log('User document does not exist in Firestore, creating it...');
          // If the document doesn't exist, we need to create it
          await setDoc(userRef, {
            displayName,
            photoURL: profilePictureUrl,
            bio: bio || '',
            profileCompleted: true,
            createdAt: new Date().toISOString(),
            role: 'reader', // Default role
          });
        } else {
          // Update user document in Firestore
          console.log('Updating Firestore user document...');
          await updateDoc(userRef, {
            displayName,
            photoURL: profilePictureUrl,
            bio: bio || '',
            profileCompleted: true,
          });
        }
        console.log('Firestore user document updated successfully');
      } catch (firestoreError) {
        console.error('Error updating Firestore document:', firestoreError);
        throw new Error(`Failed to update Firestore document: ${firestoreError instanceof Error ? firestoreError.message : 'Unknown error'}`);
      }
      
      // Redirect to home page
      console.log('Profile update complete, redirecting to home page...');
      
      // Force a hard navigation to ensure the app state is reset
      window.location.href = '/home';
    } catch (err) {
      console.error('Error completing profile:', err);
      setError(`Failed to update profile: ${err instanceof Error ? err.message : 'Unknown error'}`);
      setLoading(false);
    }
  };
  
  if (authLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black" />
      </div>
    );
  }
  
  if (!user) {
    return null; // Will redirect to sign in
  }
  
  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-6 sm:py-12 bg-gray-50">
      <Card className="w-full max-w-4xl p-4 sm:p-8">
        <div className="text-center mb-6 sm:mb-8">
          <h1 className="text-xl sm:text-2xl font-bold">Complete Your Profile</h1>
          <p className="text-gray-600 mt-2 text-sm sm:text-base">
            Choose a profile picture to continue using BookWorld.
          </p>
          <p className="text-xs sm:text-sm text-gray-500 mt-1">
            Your profile picture will be displayed in a 10:16 book cover format.
          </p>
        </div>
        
        {error && (
          <div className="mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <p className="text-red-700 text-sm sm:text-base">{error}</p>
            </div>
            {error.includes('network') && (
              <p className="mt-2 text-xs sm:text-sm text-red-600">
                Please check your internet connection and try again. If the problem persists, try refreshing the page.
              </p>
            )}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
          <div className="mb-6 sm:mb-8">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Profile Picture (Required)
            </label>
            
            <div className="flex flex-col items-center">
              {profilePictureUrl ? (
                <div className="mb-4">
                  <ProfilePicture
                    src={profilePictureUrl}
                    alt="Profile preview"
                    size="lg"
                  />
                  <button
                    type="button"
                    onClick={() => setProfilePictureUrl(null)}
                    className="mt-2 text-sm text-purple-600 hover:text-purple-800"
                  >
                    Change Picture
                  </button>
                </div>
              ) : (
                <div className="w-full">
                  <ProfilePictureSelector
                    currentImageUrl={profilePictureUrl || undefined}
                    userBooks={userBooks}
                    onSave={handleProfilePictureChange}
                    hideSaveButton={true}
                  />
                </div>
              )}
            </div>
          </div>
          
          <Input
            label="Display Name"
            id="displayName"
            name="displayName"
            value={displayName}
            onChange={(e) => setDisplayName(e.target.value)}
            required
            fullWidth
            className="mb-4"
          />
          
          <div className="mb-6">
            <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
              Bio (Optional)
            </label>
            <textarea
              id="bio"
              name="bio"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black text-sm sm:text-base"
              value={bio}
              onChange={(e) => setBio(e.target.value)}
              placeholder="Tell us a bit about yourself..."
            />
          </div>
          
          <Button
            type="submit"
            isLoading={loading}
            fullWidth
            className="mt-6"
          >
            Complete Profile
          </Button>
        </form>
      </Card>
    </div>
  );
}
