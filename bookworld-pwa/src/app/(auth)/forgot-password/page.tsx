'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import AuthLayout from '@/components/layout/AuthLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useAuthContext } from '@/lib/context/AuthContext';
import { useForm } from '@/lib/hooks/useForm';

interface ForgotPasswordFormValues {
  email: string;
}

export default function ForgotPasswordPage() {
  const { forgotPassword } = useAuthContext();
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const {
    values,
    errors,
    handleChange,
    handleBlur,
    handleSubmit,
    isSubmitting,
  } = useForm<ForgotPasswordFormValues>({
    initialValues: {
      email: '',
    },
    validate: (values) => {
      const errors: Partial<Record<keyof ForgotPasswordFormValues, string>> = {};
      
      if (!values.email) {
        errors.email = 'Email is required';
      } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email)) {
        errors.email = 'Invalid email address';
      }
      
      return errors;
    },
    onSubmit: async (values) => {
      try {
        setStatus('idle');
        setErrorMessage(null);
        await forgotPassword(values.email);
        setStatus('success');
      } catch (error: any) {
        setStatus('error');
        setErrorMessage(
          error.code === 'auth/user-not-found'
            ? 'No account found with this email address.'
            : 'Error sending password reset email. Please try again.'
        );
        console.error('Password reset error:', error);
      }
    },
  });

  return (
    <AuthLayout
      title="Reset your password"
      subtitle="Enter your email address and we'll send you a link to reset your password."
    >
      {status === 'success' ? (
        <div className="text-center">
          <div className="mb-4 p-3 bg-green-50 text-green-700 rounded-md text-sm">
            Password reset email sent! Check your inbox for further instructions.
          </div>
          <Link
            href="/signin"
            className="font-medium text-black hover:text-gray-800"
          >
            Return to sign in
          </Link>
        </div>
      ) : (
        <>
          {status === 'error' && errorMessage && (
            <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
              {errorMessage}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <Input
              label="Email address"
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              fullWidth
              value={values.email}
              onChange={handleChange}
              onBlur={handleBlur}
              error={errors.email}
            />

            <Button
              type="submit"
              fullWidth
              isLoading={isSubmitting}
            >
              Send reset link
            </Button>
            
            <div className="text-center mt-4">
              <Link
                href="/signin"
                className="text-sm font-medium text-black hover:text-gray-800"
              >
                Back to sign in
              </Link>
            </div>
          </form>
        </>
      )}
    </AuthLayout>
  );
}
