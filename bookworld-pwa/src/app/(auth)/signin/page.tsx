'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AuthLayout from '@/components/layout/AuthLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useAuthContext } from '@/lib/context/AuthContext';
import { useForm } from '@/lib/hooks/useForm';
import { getDocumentById, createDocument } from '@/lib/utils/firestore';
import type { User } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';

interface SignInFormValues {
  email: string;
  password: string;
}

export default function SignInPage() {
  const router = useRouter();
  const { login, loginWithGoogle } = useAuthContext();
  const [authError, setAuthError] = useState<string | null>(null);

  const {
    values,
    errors,
    handleChange,
    handleBlur,
    handleSubmit,
    isSubmitting,
  } = useForm<SignInFormValues>({
    initialValues: {
      email: '',
      password: '',
    },
    validate: (values) => {
      const errors: Partial<Record<keyof SignInFormValues, string>> = {};

      if (!values.email) {
        errors.email = 'Email is required';
      } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email)) {
        errors.email = 'Invalid email address';
      }

      if (!values.password) {
        errors.password = 'Password is required';
      }

      return errors;
    },
    onSubmit: async (values) => {
      try {
        setAuthError(null);
        await login(values.email, values.password);
        router.push('/');
      } catch (error: unknown) {
        const err = error as { code?: string };
        if (err.code === 'auth/invalid-credential' || err.code === 'auth/wrong-password' || err.code === 'auth/user-not-found') {
          setAuthError('Invalid email or password. Please try again.');
        } else if (err.code === 'auth/too-many-requests') {
          setAuthError('Too many failed login attempts. Please try again later or reset your password.');
        } else {
          setAuthError('An error occurred during sign in. Please try again.');
        }
        console.error('Login error:', error);
      }
    },
  });

  const [showRoleSelection, setShowRoleSelection] = useState(false);
  const [selectedRole, setSelectedRole] = useState<'reader' | 'author'>('reader');
  const [googleUser, setGoogleUser] = useState<User | null>(null);

  const handleGoogleSignIn = async () => {
    try {
      setAuthError(null);
      await loginWithGoogle();

      // Get the current user after successful login
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        throw new Error('Failed to get user after Google sign-in');
      }

      // Check if user already exists in Firestore
      const userDoc = await getDocumentById('users', currentUser.uid);

      if (userDoc) {
        // User exists, proceed to home page
        router.push('/');
      } else {
        // New user, store credentials temporarily and show role selection
        setGoogleUser(currentUser);
        setShowRoleSelection(true);
      }
    } catch (error: unknown) {
      const err = error as { code?: string };
      if (err.code === 'auth/popup-closed-by-user') {
        setAuthError('Sign-in popup was closed before completing the sign in.');
      } else if (err.code === 'auth/cancelled-popup-request') {
        // This is a normal case when user clicks the button multiple times
        // No need to show an error
      } else if (err.code === 'auth/popup-blocked') {
        setAuthError('Sign-in popup was blocked by the browser. Please allow popups for this site.');
      } else {
        setAuthError('Error signing in with Google. Please try again.');
      }
      console.error('Google sign-in error:', error);
    }
  };

  const handleRoleConfirm = async () => {
    try {
      if (!googleUser) return;

      // Create user profile in Firestore with selected role
      await createDocument('users', googleUser.uid, {
        name: googleUser.displayName || 'User',
        email: googleUser.email || '',
        role: selectedRole,
        createdAt: new Date(),
        points: 0,
        trophies: [],
      });

      router.push('/');
    } catch (error: unknown) {
      setAuthError('Error creating user profile. Please try again.');
      console.error('Profile creation error:', error);
      // Reset the state to allow the user to try again
      setShowRoleSelection(false);
      setGoogleUser(null);
    }
  };

  return (
    <AuthLayout
      title="Sign in to your account"
      showSignUpLink
    >
      {authError && (
        <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
          {authError}
        </div>
      )}

      {showRoleSelection ? (
        <div className="space-y-6">
          <p className="text-center text-sm text-gray-600 mb-4">
            Welcome to BookWorld! Please select your role to continue.
          </p>

          <div className="mt-4">
            <p className="block text-sm font-medium text-gray-700 mb-2">I am joining as</p>
            <div className="flex space-x-4">
              <label className={`flex-1 flex items-center justify-center p-3 border rounded-md cursor-pointer transition-colors ${selectedRole === 'reader' ? 'bg-black text-white border-black' : 'border-gray-300 hover:bg-gray-50'}`}>
                <input
                  type="radio"
                  name="role"
                  value="reader"
                  checked={selectedRole === 'reader'}
                  onChange={() => setSelectedRole('reader')}
                  className="sr-only"
                />
                <span className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                  Reader
                </span>
              </label>
              <label className={`flex-1 flex items-center justify-center p-3 border rounded-md cursor-pointer transition-colors ${selectedRole === 'author' ? 'bg-black text-white border-black' : 'border-gray-300 hover:bg-gray-50'}`}>
                <input
                  type="radio"
                  name="role"
                  value="author"
                  checked={selectedRole === 'author'}
                  onChange={() => setSelectedRole('author')}
                  className="sr-only"
                />
                <span className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                  Author
                </span>
              </label>
            </div>
          </div>

          <Button
            type="button"
            fullWidth
            onClick={handleRoleConfirm}
          >
            Continue
          </Button>

          <Button
            type="button"
            fullWidth
            variant="ghost"
            onClick={() => setShowRoleSelection(false)}
          >
            Back to Sign In
          </Button>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
        <Input
          label="Email address"
          id="email"
          name="email"
          type="email"
          autoComplete="email"
          required
          fullWidth
          value={values.email}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.email}
        />

        <div>
          <div className="flex items-center justify-between">
            <Input
              label="Password"
              id="password"
              name="password"
              type="password"
              autoComplete="current-password"
              required
              fullWidth
              value={values.password}
              onChange={handleChange}
              onBlur={handleBlur}
              error={errors.password}
            />
          </div>
          <div className="text-right mt-1">
            <Link
              href="/forgot-password"
              className="text-sm font-medium text-black hover:text-gray-800"
            >
              Forgot your password?
            </Link>
          </div>
        </div>

        <Button
          type="submit"
          fullWidth
          isLoading={isSubmitting}
        >
          Sign in
        </Button>
      </form>
      )}

      {!showRoleSelection && <div className="mt-6">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or continue with</span>
          </div>
        </div>

        <div className="mt-6">
          <Button
            type="button"
            fullWidth
            variant="outline"
            onClick={handleGoogleSignIn}
          >
            <svg
              className="w-5 h-5 mr-2"
              viewBox="0 0 24 24"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                fill="#4285F4"
              />
              <path
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                fill="#34A853"
              />
              <path
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                fill="#FBBC05"
              />
              <path
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                fill="#EA4335"
              />
            </svg>
            Sign in with Google
          </Button>
        </div>
      </div>}
    </AuthLayout>
  );
}
