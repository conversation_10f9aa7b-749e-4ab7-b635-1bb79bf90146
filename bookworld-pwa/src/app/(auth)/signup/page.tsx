'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import AuthLayout from '@/components/layout/AuthLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useAuthContext } from '@/lib/context/AuthContext';
import { useForm } from '@/lib/hooks/useForm';
import { createDocument } from '@/lib/utils/firestore';

interface SignUpFormValues {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: 'author' | 'reader';
}

export default function SignUpPage() {
  const router = useRouter();
  const { register, loginWithGoogle } = useAuthContext();
  const [authError, setAuthError] = useState<string | null>(null);

  const {
    values,
    errors,
    handleChange,
    handleBlur,
    handleSubmit,
    isSubmitting,
  } = useForm<SignUpFormValues>({
    initialValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      role: 'reader',
    },
    validate: (values) => {
      const errors: Partial<Record<keyof SignUpFormValues, string>> = {};

      if (!values.name) {
        errors.name = 'Name is required';
      }

      if (!values.email) {
        errors.email = 'Email is required';
      } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email)) {
        errors.email = 'Invalid email address';
      }

      if (!values.password) {
        errors.password = 'Password is required';
      } else if (values.password.length < 6) {
        errors.password = 'Password must be at least 6 characters';
      }

      if (!values.confirmPassword) {
        errors.confirmPassword = 'Please confirm your password';
      } else if (values.password !== values.confirmPassword) {
        errors.confirmPassword = 'Passwords do not match';
      }

      return errors;
    },
    onSubmit: async (values) => {
      try {
        setAuthError(null);
        const userCredential = await register(values.email, values.password);

        // Create user profile in Firestore
        await createDocument('users', userCredential.user.uid, {
          displayName: values.name,
          email: values.email,
          role: values.role,
          createdAt: new Date(),
          following: [],
          followers: [],
          bio: '',
          points: 0,
          trophies: [],
        });

        router.push('/');
      } catch (error: unknown) {
        const err = error as { code?: string };
        if (err.code === 'auth/email-already-in-use') {
          setAuthError('This email is already in use. Please try another one.');
        } else {
          setAuthError('Error creating account. Please try again.');
        }
        console.error('Registration error:', error);
      }
    },
  });

  const handleGoogleSignIn = async () => {
    try {
      setAuthError(null);
      const userCredential = await loginWithGoogle();

      // Create or update user profile in Firestore with selected role
      await createDocument('users', userCredential.user.uid, {
        displayName: userCredential.user.displayName || 'User',
        email: userCredential.user.email || '',
        role: values.role,
        createdAt: new Date(),
        following: [],
        followers: [],
        bio: '',
        points: 0,
        trophies: [],
      });

      router.push('/');
    } catch (error: unknown) {
      setAuthError('Error signing in with Google. Please try again.');
      console.error('Google sign-in error:', error);
    }
  };

  return (
    <AuthLayout
      title="Create your account"
      showSignInLink
    >
      {authError && (
        <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
          {authError}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <Input
          label="Full name"
          id="name"
          name="name"
          type="text"
          autoComplete="name"
          required
          fullWidth
          value={values.name}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.name}
        />

        <Input
          label="Email address"
          id="email"
          name="email"
          type="email"
          autoComplete="email"
          required
          fullWidth
          value={values.email}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.email}
        />

        <Input
          label="Password"
          id="password"
          name="password"
          type="password"
          autoComplete="new-password"
          required
          fullWidth
          value={values.password}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.password}
          helperText="Password must be at least 6 characters"
        />

        <Input
          label="Confirm password"
          id="confirmPassword"
          name="confirmPassword"
          type="password"
          autoComplete="new-password"
          required
          fullWidth
          value={values.confirmPassword}
          onChange={handleChange}
          onBlur={handleBlur}
          error={errors.confirmPassword}
        />

        <div className="mt-4">
          <p className="block text-sm font-medium text-gray-700 mb-2">I am joining as</p>
          <div className="flex space-x-4">
            <label className={`flex-1 flex items-center justify-center p-3 border rounded-md cursor-pointer transition-colors ${values.role === 'reader' ? 'bg-black text-white border-black' : 'border-gray-300 hover:bg-gray-50'}`}>
              <input
                type="radio"
                name="role"
                value="reader"
                checked={values.role === 'reader'}
                onChange={handleChange}
                className="sr-only"
              />
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                Reader
              </span>
            </label>
            <label className={`flex-1 flex items-center justify-center p-3 border rounded-md cursor-pointer transition-colors ${values.role === 'author' ? 'bg-black text-white border-black' : 'border-gray-300 hover:bg-gray-50'}`}>
              <input
                type="radio"
                name="role"
                value="author"
                checked={values.role === 'author'}
                onChange={handleChange}
                className="sr-only"
              />
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
                Author
              </span>
            </label>
          </div>
        </div>

        <Button
          type="submit"
          fullWidth
          isLoading={isSubmitting}
        >
          Sign up
        </Button>
      </form>

      <div className="mt-6">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or continue with</span>
          </div>
        </div>

        <div className="mt-6">
          <Button
            type="button"
            fullWidth
            variant="outline"
            onClick={handleGoogleSignIn}
          >
            <svg
              className="w-5 h-5 mr-2"
              viewBox="0 0 24 24"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                fill="#4285F4"
              />
              <path
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                fill="#34A853"
              />
              <path
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                fill="#FBBC05"
              />
              <path
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                fill="#EA4335"
              />
            </svg>
            Sign up with Google
          </Button>
        </div>
      </div>
    </AuthLayout>
  );
}
