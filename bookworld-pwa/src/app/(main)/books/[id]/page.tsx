'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/lib/context/AuthContext';
import { getBookById, hasUserPurchasedBook, getReadingProgress } from '@/lib/utils/books';
import { formatCurrency, formatDate } from '@/lib/utils/format';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import BookCard, { BookData } from '@/components/books/BookCard';

interface BookDetailsPageProps {
  params: {
    id: string;
  };
}

export default function BookDetailsPage({ params }: BookDetailsPageProps) {
  const { id } = params;
  const router = useRouter();
  const { user, loading: authLoading } = useAuthContext();
  
  const [book, setBook] = useState<BookData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [isPurchased, setIsPurchased] = useState<boolean>(false);
  const [readingProgress, setReadingProgress] = useState<number>(0);
  const [relatedBooks, setRelatedBooks] = useState<BookData[]>([]);
  const [activeTab, setActiveTab] = useState<'description' | 'reviews' | 'details'>('description');
  
  useEffect(() => {
    const fetchBookDetails = async () => {
      if (authLoading) return;
      
      if (!user) {
        router.push('/signin');
        return;
      }
      
      try {
        setLoading(true);
        
        // Get book details
        const bookDetails = await getBookById(id);
        
        if (!bookDetails) {
          router.push('/not-found');
          return;
        }
        
        setBook({
          id: bookDetails.id,
          title: bookDetails.title,
          author: bookDetails.author,
          coverUrl: bookDetails.coverUrl,
          price: bookDetails.price,
          description: bookDetails.description,
          rating: bookDetails.rating,
          reviewCount: bookDetails.reviewCount,
          publishedAt: bookDetails.publishedAt.toDate(),
          tags: bookDetails.tags,
        });
        
        // Check if user has purchased the book
        const purchased = await hasUserPurchasedBook(user.uid, id);
        setIsPurchased(purchased);
        
        // Get reading progress if purchased
        if (purchased) {
          const progress = await getReadingProgress(user.uid, id);
          if (progress) {
            setReadingProgress(progress.progress);
          }
        }
        
        // TODO: Fetch related books
        
        setLoading(false);
      } catch (error) {
        console.error('Error fetching book details:', error);
        setLoading(false);
      }
    };
    
    fetchBookDetails();
  }, [id, user, authLoading, router]);
  
  const handleBuyBook = () => {
    router.push(`/checkout/${id}`);
  };
  
  const handleReadBook = () => {
    router.push(`/reader/${id}`);
  };
  
  if (loading || authLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
      </div>
    );
  }
  
  if (!book) {
    return null;
  }
  
  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Book cover and actions */}
        <div className="md:w-1/3">
          <div className="relative aspect-[2/3] w-full rounded-lg overflow-hidden shadow-lg">
            <Image
              src={book.coverUrl}
              alt={book.title}
              fill
              className="object-cover"
            />
          </div>
          
          <div className="mt-6 space-y-4">
            {isPurchased ? (
              <>
                <Button
                  fullWidth
                  onClick={handleReadBook}
                >
                  Continue Reading
                </Button>
                
                {readingProgress > 0 && (
                  <div className="mt-2">
                    <div className="text-sm text-gray-500 mb-1">
                      {readingProgress}% completed
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-black rounded-full h-2"
                        style={{ width: `${readingProgress}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <>
                <Button
                  fullWidth
                  onClick={handleBuyBook}
                >
                  {book.price === 0 ? 'Get for Free' : `Buy for ${formatCurrency(book.price)}`}
                </Button>
                
                <Button
                  fullWidth
                  variant="outline"
                >
                  Add to Wishlist
                </Button>
              </>
            )}
          </div>
        </div>
        
        {/* Book details */}
        <div className="md:w-2/3">
          <h1 className="text-3xl font-bold mb-2">{book.title}</h1>
          
          <div className="flex items-center mb-4">
            <div 
              className="cursor-pointer flex items-center"
              onClick={() => router.push(`/authors/${book.author.id}`)}
            >
              {book.author.photoUrl ? (
                <Image
                  src={book.author.photoUrl}
                  alt={book.author.name}
                  width={32}
                  height={32}
                  className="rounded-full mr-2"
                />
              ) : (
                <div className="w-8 h-8 rounded-full bg-gray-200 mr-2 flex items-center justify-center text-sm">
                  {book.author.name.charAt(0)}
                </div>
              )}
              <span className="text-gray-700 hover:underline">{book.author.name}</span>
            </div>
            
            <span className="mx-2 text-gray-400">•</span>
            
            <span className="text-gray-500">
              Published {formatDate(book.publishedAt)}
            </span>
          </div>
          
          {book.rating && (
            <div className="flex items-center mb-4">
              <div className="flex items-center">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg
                    key={star}
                    xmlns="http://www.w3.org/2000/svg"
                    className={`h-5 w-5 ${star <= Math.round(book.rating) ? 'text-yellow-500' : 'text-gray-300'}`}
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <span className="ml-2 text-gray-700">
                {book.rating.toFixed(1)}
              </span>
              {book.reviewCount && (
                <span className="ml-1 text-gray-500">
                  ({book.reviewCount} {book.reviewCount === 1 ? 'review' : 'reviews'})
                </span>
              )}
            </div>
          )}
          
          {book.tags && book.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-6">
              {book.tags.map(tag => (
                <span 
                  key={tag} 
                  className="text-xs bg-gray-100 text-gray-600 px-3 py-1 rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          )}
          
          {/* Tabs */}
          <div className="border-b border-gray-200 mb-6">
            <nav className="-mb-px flex space-x-8">
              <button
                className={`pb-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'description'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('description')}
              >
                Description
              </button>
              <button
                className={`pb-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'reviews'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('reviews')}
              >
                Reviews
              </button>
              <button
                className={`pb-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'details'
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('details')}
              >
                Details
              </button>
            </nav>
          </div>
          
          {/* Tab content */}
          <div>
            {activeTab === 'description' && (
              <div className="prose max-w-none">
                <p className="whitespace-pre-line">{book.description}</p>
              </div>
            )}
            
            {activeTab === 'reviews' && (
              <div>
                {/* TODO: Implement reviews */}
                <p className="text-gray-500">No reviews yet. Be the first to review this book!</p>
              </div>
            )}
            
            {activeTab === 'details' && (
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Publisher</h3>
                  <p>BookWorld Publishing</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Publication Date</h3>
                  <p>{formatDate(book.publishedAt)}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Language</h3>
                  <p>English</p>
                </div>
                {/* Add more details as needed */}
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Related books */}
      {relatedBooks.length > 0 && (
        <div className="mt-16">
          <h2 className="text-2xl font-bold mb-6">You might also like</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {relatedBooks.map((relatedBook) => (
              <BookCard
                key={relatedBook.id}
                book={relatedBook}
                variant="grid"
                showBuyButton
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
