'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/lib/context/AuthContext';
import { useCollection } from '@/lib/hooks/useFirestore';
import { where, orderBy, limit } from 'firebase/firestore';
import Button from '@/components/ui/Button';
import AudioPost, { type AudioPostData } from '@/components/posts/AudioPost';
import CreateAudioPost from '@/components/posts/CreateAudioPost';
import { createAudioPost, likePost, followUser } from '@/lib/utils/posts';

interface Post {
  id: string;
  title: string;
  audioUrl: string;
  coverUrl?: string;
  authorId: string;
  authorName: string;
  authorPhotoUrl?: string;
  duration: number; // in seconds
  waveformData?: number[];
  likes: number;
  comments: number;
  createdAt: { toDate: () => Date } | Date;
}

export default function HomePage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuthContext();
  const [activeTab, setActiveTab] = useState<'following' | 'trending' | 'latest'>('following');
  const [showCreatePost, setShowCreatePost] = useState(false);

  // Redirect to sign in if not authenticated or if user doesn't have a profile picture
  useEffect(() => {
    if (!authLoading) {
      if (!user) {
        router.push('/signin');
      } else if (!user.photoURL) {
        // Redirect to complete profile if user doesn't have a profile picture
        router.push('/complete-profile');
      }
    }
  }, [user, authLoading, router]);

  // Get posts from users the current user follows
  const {
    documents: followingPosts,
    loading: followingLoading
  } = useCollection<Post>(
    'posts',
    user ? [
      where('authorId', 'in', [
        user.uid,
        ...(user.following && user.following.length > 0
          ? user.following.slice(0, 9) // Firestore 'in' query supports max 10 values
          : [])
      ]),
      orderBy('createdAt', 'desc'),
      limit(20)
    ] : []
  );

  // Get trending posts (most likes)
  const {
    documents: trendingPosts,
    loading: trendingLoading
  } = useCollection<Post>(
    'posts',
    [orderBy('likes', 'desc'), limit(20)]
  );

  // Get latest posts
  const {
    documents: latestPosts,
    loading: latestLoading
  } = useCollection<Post>(
    'posts',
    [orderBy('createdAt', 'desc'), limit(20)]
  );

  let posts: Post[] | undefined;
  let isLoading: boolean;

  if (activeTab === 'following') {
    posts = followingPosts;
    isLoading = authLoading || followingLoading;
  } else if (activeTab === 'trending') {
    posts = trendingPosts;
    isLoading = authLoading || trendingLoading;
  } else {
    posts = latestPosts;
    isLoading = authLoading || latestLoading;
  }

  const handleCreatePost = async (data: {
    title: string;
    audioFile: File;
    coverFile?: File;
    duration: number;
  }) => {
    if (!user) return;

    try {
      // Generate random waveform data for visualization
      const waveformData = Array.from({ length: 50 }, () => Math.random() * 0.8 + 0.2);

      await createAudioPost({
        userId: user.uid,
        userName: user.displayName || 'Anonymous',
        userPhotoUrl: user.photoURL || undefined,
        title: data.title,
        audioFile: data.audioFile,
        coverFile: data.coverFile,
        duration: data.duration,
        waveformData,
      });

      // Hide the create post form after successful submission
      setShowCreatePost(false);
    } catch (error) {
      console.error('Error creating post:', error);
    }
  };

  const handleLikePost = async (postId: string) => {
    if (!user) return;
    await likePost(postId, user.uid);
  };

  const handleCommentPost = (postId: string) => {
    router.push(`/post/${postId}`);
  };

  const handleFollowUser = async (authorId: string) => {
    if (!user || authorId === user.uid) return;
    await followUser(user.uid, authorId);
  };

  if (authLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black" />
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to sign in
  }

  return (
    <div className="max-w-2xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold">BookWorld</h1>
        <Button
          onClick={() => setShowCreatePost(!showCreatePost)}
          size="sm"
        >
          {showCreatePost ? 'Cancel' : 'Create Post'}
        </Button>
      </div>

      {showCreatePost && (
        <CreateAudioPost onSubmit={handleCreatePost} />
      )}

      <div className="flex border-b border-gray-200 mb-6">
        <button
          type="button"
          className={`pb-2 px-4 text-sm font-medium ${
            activeTab === 'following'
              ? 'border-b-2 border-black text-black'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('following')}
        >
          Following
        </button>
        <button
          type="button"
          className={`pb-2 px-4 text-sm font-medium ${
            activeTab === 'trending'
              ? 'border-b-2 border-black text-black'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('trending')}
        >
          Trending
        </button>
        <button
          type="button"
          className={`pb-2 px-4 text-sm font-medium ${
            activeTab === 'latest'
              ? 'border-b-2 border-black text-black'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('latest')}
        >
          Latest
        </button>
      </div>

      {isLoading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black" />
        </div>
      ) : posts && posts.length > 0 ? (
        <div className="space-y-12">
          {posts.map((post) => {
            // Convert Firestore timestamp to Date if needed
            const createdAt = post.createdAt instanceof Date
              ? post.createdAt
              : post.createdAt.toDate();

            // Convert to AudioPostData format
            const audioPost: AudioPostData = {
              id: post.id,
              authorId: post.authorId,
              authorName: post.authorName,
              authorPhotoUrl: post.authorPhotoUrl,
              title: post.title,
              audioUrl: post.audioUrl,
              coverUrl: post.coverUrl,
              duration: post.duration,
              waveformData: post.waveformData,
              likes: post.likes,
              comments: post.comments,
              createdAt: createdAt,
              isFollowing: user?.following?.includes(post.authorId) || post.authorId === user?.uid
            };

            return (
              <AudioPost
                key={post.id}
                post={audioPost}
                onLike={handleLikePost}
                onComment={handleCommentPost}
                onFollow={handleFollowUser}
              />
            );
          })}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">
            {activeTab === 'following'
              ? "You're not following anyone yet or they haven't posted anything."
              : "No posts found."}
          </p>
          <Button
            onClick={() => setShowCreatePost(true)}
          >
            Create your first post
          </Button>
        </div>
      )}
    </div>
  );
}
