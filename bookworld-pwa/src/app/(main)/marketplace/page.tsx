'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/lib/context/AuthContext';
import { getFeaturedBooks, getLatestBooks } from '@/lib/utils/books';
import BookCard, { BookData } from '@/components/books/BookCard';
import Button from '@/components/ui/Button';

export default function MarketplacePage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuthContext();
  
  const [featuredBooks, setFeaturedBooks] = useState<BookData[]>([]);
  const [latestBooks, setLatestBooks] = useState<BookData[]>([]);
  const [popularAuthors, setPopularAuthors] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeCategory, setActiveCategory] = useState<string>('all');
  
  const categories = [
    { id: 'all', name: 'All' },
    { id: 'fiction', name: 'Fiction' },
    { id: 'non-fiction', name: 'Non-Fiction' },
    { id: 'fantasy', name: 'Fantasy' },
    { id: 'sci-fi', name: 'Sci-Fi' },
    { id: 'romance', name: 'Romance' },
    { id: 'mystery', name: 'Mystery' },
    { id: 'biography', name: 'Biography' },
  ];
  
  useEffect(() => {
    const fetchBooks = async () => {
      if (authLoading) return;
      
      if (!user) {
        router.push('/signin');
        return;
      }
      
      try {
        setLoading(true);
        
        // Get featured books
        const featured = await getFeaturedBooks(6);
        setFeaturedBooks(featured);
        
        // Get latest books
        const latest = await getLatestBooks(8);
        setLatestBooks(latest);
        
        // Example popular authors data
        setPopularAuthors([
          { id: '1', name: 'J.K. Rowling', photoUrl: 'https://via.placeholder.com/150', bookCount: 12, followers: 15000 },
          { id: '2', name: 'Stephen King', photoUrl: 'https://via.placeholder.com/150', bookCount: 65, followers: 25000 },
          { id: '3', name: 'James Patterson', photoUrl: 'https://via.placeholder.com/150', bookCount: 48, followers: 18000 },
          { id: '4', name: 'Danielle Steel', photoUrl: 'https://via.placeholder.com/150', bookCount: 179, followers: 12000 },
        ]);
        
        setLoading(false);
      } catch (error) {
        console.error('Error fetching books:', error);
        setLoading(false);
      }
    };
    
    fetchBooks();
  }, [user, authLoading, router]);
  
  if (loading || authLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black" />
      </div>
    );
  }
  
  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">BookWorld Marketplace</h1>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push('/marketplace/search')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
            </svg>
            Search
          </Button>
          {user && user.role === 'author' && (
            <Button
              onClick={() => router.push('/author/books/new')}
            >
              Publish Book
            </Button>
          )}
        </div>
      </div>
      
      {/* Categories */}
      <div className="mb-8 overflow-x-auto">
        <div className="flex space-x-2 pb-2">
          {categories.map((category) => (
            <button
              key={category.id}
              type="button"
              className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap ${
                activeCategory === category.id
                  ? 'bg-black text-white'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
              onClick={() => setActiveCategory(category.id)}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>
      
      {/* Featured Books */}
      <section className="mb-12">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Featured Books</h2>
          <Button
            variant="ghost"
            onClick={() => router.push('/marketplace/featured')}
          >
            View All
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredBooks.map((book) => (
            <BookCard
              key={book.id}
              book={book}
              showBuyButton
            />
          ))}
        </div>
      </section>
      
      {/* Latest Releases */}
      <section className="mb-12">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Latest Releases</h2>
          <Button
            variant="ghost"
            onClick={() => router.push('/marketplace/latest')}
          >
            View All
          </Button>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {latestBooks.map((book) => (
            <BookCard
              key={book.id}
              book={book}
              variant="grid"
              showBuyButton
            />
          ))}
        </div>
      </section>
      
      {/* Popular Authors */}
      <section className="mb-12">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Popular Authors</h2>
          <Button
            variant="ghost"
            onClick={() => router.push('/marketplace/authors')}
          >
            View All
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {popularAuthors.map((author) => (
            <div
              key={author.id}
              className="bg-white border border-gray-200 rounded-lg p-4 flex flex-col items-center text-center cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push(`/authors/${author.id}`)}
            >
              <div className="w-20 h-20 rounded-full overflow-hidden mb-4">
                <img
                  src={author.photoUrl}
                  alt={author.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="font-bold text-lg mb-1">{author.name}</h3>
              <p className="text-sm text-gray-500 mb-2">{author.bookCount} books</p>
              <p className="text-xs text-gray-400">{author.followers.toLocaleString()} followers</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-3"
                onClick={(e) => {
                  e.stopPropagation();
                  // Follow author logic
                }}
              >
                Follow
              </Button>
            </div>
          ))}
        </div>
      </section>
      
      {/* Special Deals */}
      <section className="mb-12">
        <div className="bg-gray-100 rounded-lg p-6">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-4 md:mb-0">
              <h2 className="text-2xl font-bold mb-2">Special Deals</h2>
              <p className="text-gray-600 max-w-md">
                Discover our special deals and promotions. Get up to 50% off on selected books!
              </p>
              <Button
                className="mt-4"
                onClick={() => router.push('/marketplace/deals')}
              >
                Browse Deals
              </Button>
            </div>
            <div className="flex space-x-4">
              <div className="bg-white p-3 rounded-lg shadow-md">
                <div className="text-xs text-gray-500 mb-1">Deal ends in</div>
                <div className="text-xl font-bold">2 days</div>
              </div>
              <div className="bg-white p-3 rounded-lg shadow-md">
                <div className="text-xs text-gray-500 mb-1">Books on sale</div>
                <div className="text-xl font-bold">250+</div>
              </div>
              <div className="bg-white p-3 rounded-lg shadow-md">
                <div className="text-xs text-gray-500 mb-1">Max discount</div>
                <div className="text-xl font-bold">50%</div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Reading Clubs */}
      <section>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Reading Clubs</h2>
          <Button
            variant="ghost"
            onClick={() => router.push('/clubs')}
          >
            View All
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
            <div className="h-32 bg-gradient-to-r from-blue-500 to-purple-500"></div>
            <div className="p-4">
              <h3 className="font-bold text-lg mb-1">Fantasy Book Club</h3>
              <p className="text-sm text-gray-500 mb-3">
                Join 1,250 readers discussing the latest fantasy novels
              </p>
              <div className="flex justify-between items-center">
                <div className="flex -space-x-2">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="w-8 h-8 rounded-full border-2 border-white bg-gray-300"></div>
                  ))}
                </div>
                <Button
                  size="sm"
                  onClick={() => router.push('/clubs/fantasy')}
                >
                  Join
                </Button>
              </div>
            </div>
          </div>
          
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
            <div className="h-32 bg-gradient-to-r from-green-500 to-teal-500"></div>
            <div className="p-4">
              <h3 className="font-bold text-lg mb-1">Science & Technology</h3>
              <p className="text-sm text-gray-500 mb-3">
                Explore cutting-edge science with 980 curious minds
              </p>
              <div className="flex justify-between items-center">
                <div className="flex -space-x-2">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="w-8 h-8 rounded-full border-2 border-white bg-gray-300"></div>
                  ))}
                </div>
                <Button
                  size="sm"
                  onClick={() => router.push('/clubs/science')}
                >
                  Join
                </Button>
              </div>
            </div>
          </div>
          
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
            <div className="h-32 bg-gradient-to-r from-red-500 to-orange-500"></div>
            <div className="p-4">
              <h3 className="font-bold text-lg mb-1">Mystery & Thrillers</h3>
              <p className="text-sm text-gray-500 mb-3">
                Unravel mysteries with 1,540 fellow detectives
              </p>
              <div className="flex justify-between items-center">
                <div className="flex -space-x-2">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="w-8 h-8 rounded-full border-2 border-white bg-gray-300"></div>
                  ))}
                </div>
                <Button
                  size="sm"
                  onClick={() => router.push('/clubs/mystery')}
                >
                  Join
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
