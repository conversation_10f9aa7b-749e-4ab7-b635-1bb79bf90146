'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

export default function CoverCreatorPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-100 py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Book Cover Creator</h1>
          <Button
            variant="outline"
            onClick={() => router.push('/')}
          >
            Back to Home
          </Button>
        </div>
        
        <Card className="p-8 mb-8 text-center">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold mb-4">Create Beautiful Book Covers</h2>
            <p className="text-gray-600 mb-8">
              Our cover creator is coming soon! Design stunning covers for your books with our intuitive tools.
              Be notified when this feature launches.
            </p>
            
            <div className="relative w-full max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="w-full px-4 py-3 pr-24 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
              />
              <Button
                className="absolute right-1 top-1 bottom-1"
                onClick={() => {
                  setLoading(true);
                  setTimeout(() => {
                    setLoading(false);
                    alert('Thank you! We\'ll notify you when the cover creator launches.');
                  }, 1000);
                }}
                isLoading={loading}
              >
                Notify Me
              </Button>
            </div>
          </div>
        </Card>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z" />
              </svg>
            </div>
            <h3 className="text-lg font-bold mb-2">Customizable Templates</h3>
            <p className="text-gray-600">
              Choose from dozens of professionally designed templates and customize them to fit your book's genre and style.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-bold mb-2">High-Quality Images</h3>
            <p className="text-gray-600">
              Access a library of millions of royalty-free images or upload your own to create the perfect cover.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
              </svg>
            </div>
            <h3 className="text-lg font-bold mb-2">Typography Tools</h3>
            <p className="text-gray-600">
              Experiment with hundreds of fonts and text effects to make your title and author name stand out.
            </p>
          </div>
        </div>
        
        <div className="bg-gray-50 p-8 rounded-lg border border-gray-200">
          <h2 className="text-2xl font-bold mb-4 text-center">Why Great Covers Matter</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-bold mb-2">First Impressions Count</h3>
              <p className="text-gray-600 mb-4">
                Studies show that readers make judgments about a book within seconds based on its cover. A professional cover can increase your book's visibility and appeal.
              </p>
              
              <h3 className="text-lg font-bold mb-2">Genre Recognition</h3>
              <p className="text-gray-600">
                Readers use visual cues to identify books in their preferred genres. A well-designed cover communicates your book's genre at a glance.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-bold mb-2">Sales Impact</h3>
              <p className="text-gray-600 mb-4">
                Books with professional covers can sell up to 50% more copies than those with amateur designs, even when the content is identical.
              </p>
              
              <h3 className="text-lg font-bold mb-2">Brand Building</h3>
              <p className="text-gray-600">
                A consistent cover style across your books helps readers recognize your work and builds your author brand.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
