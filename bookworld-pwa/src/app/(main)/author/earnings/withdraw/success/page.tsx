'use client';

import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

export default function WithdrawSuccessPage() {
  const router = useRouter();
  
  return (
    <div className="max-w-3xl mx-auto px-4 py-8">
      <Card className="p-8 text-center">
        <div className="flex justify-center mb-6">
          <div className="rounded-full bg-green-100 p-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12 text-green-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
        </div>
        
        <h1 className="text-2xl font-bold mb-2">Withdrawal Successful!</h1>
        <p className="text-gray-600 mb-6">
          Your withdrawal request has been successfully submitted. The funds will be transferred to your account within 3-5 business days.
        </p>
        
        <div className="bg-gray-50 p-4 rounded-md mb-6 max-w-sm mx-auto">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Transaction ID:</span>
              <span className="font-medium">TRX-********</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Date:</span>
              <span className="font-medium">{new Date().toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Amount:</span>
              <span className="font-medium">$250.00</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Status:</span>
              <span className="font-medium text-yellow-600">Processing</span>
            </div>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-3">
          <Button
            variant="outline"
            onClick={() => router.push('/author/earnings/history')}
          >
            View Transaction History
          </Button>
          <Button
            onClick={() => router.push('/author/dashboard')}
          >
            Return to Dashboard
          </Button>
        </div>
      </Card>
    </div>
  );
}
