'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/lib/context/AuthContext';
import { formatCurrency } from '@/lib/utils/format';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';

export default function WithdrawEarningsPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuthContext();
  
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [availableBalance, setAvailableBalance] = useState<number>(1250.50);
  const [withdrawAmount, setWithdrawAmount] = useState<string>('');
  const [paymentMethod, setPaymentMethod] = useState<'paypal' | 'bank'>('paypal');
  const [paymentDetails, setPaymentDetails] = useState({
    paypalEmail: '',
    bankName: '',
    accountNumber: '',
    routingNumber: '',
    accountHolderName: '',
  });
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const checkAuthorStatus = async () => {
      if (authLoading) return;
      
      if (!user) {
        router.push('/signin');
        return;
      }
      
      // Check if user is an author
      if (user.role !== 'author') {
        router.push('/');
        return;
      }
      
      // In a real app, we would fetch the author's available balance here
      setLoading(false);
    };
    
    checkAuthorStatus();
  }, [user, authLoading, router]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    if (name === 'withdrawAmount') {
      // Only allow numbers and a single decimal point
      if (/^\d*\.?\d*$/.test(value)) {
        setWithdrawAmount(value);
      }
    } else {
      setPaymentDetails((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setError(null);
      setSubmitting(true);
      
      const amount = parseFloat(withdrawAmount);
      
      // Validate amount
      if (isNaN(amount) || amount <= 0) {
        throw new Error('Please enter a valid amount');
      }
      
      if (amount > availableBalance) {
        throw new Error('Withdrawal amount exceeds available balance');
      }
      
      // Validate payment details
      if (paymentMethod === 'paypal' && !paymentDetails.paypalEmail) {
        throw new Error('Please enter your PayPal email');
      }
      
      if (paymentMethod === 'bank') {
        if (!paymentDetails.bankName) {
          throw new Error('Please enter your bank name');
        }
        if (!paymentDetails.accountNumber) {
          throw new Error('Please enter your account number');
        }
        if (!paymentDetails.routingNumber) {
          throw new Error('Please enter your routing number');
        }
        if (!paymentDetails.accountHolderName) {
          throw new Error('Please enter the account holder name');
        }
      }
      
      // In a real app, we would process the withdrawal here
      
      // Simulate processing delay
      await new Promise((resolve) => setTimeout(resolve, 2000));
      
      // Redirect to success page
      router.push('/author/earnings/withdraw/success');
    } catch (error) {
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('An error occurred. Please try again.');
      }
      setSubmitting(false);
    }
  };
  
  if (loading || authLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black" />
      </div>
    );
  }
  
  return (
    <div className="max-w-3xl mx-auto px-4 py-8">
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.push('/author/dashboard')}
          className="mb-4"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Dashboard
        </Button>
        <h1 className="text-3xl font-bold">Withdraw Earnings</h1>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="p-4">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Available Balance</h3>
          <p className="text-2xl font-bold">{formatCurrency(availableBalance)}</p>
        </Card>
        
        <Card className="p-4">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Pending Balance</h3>
          <p className="text-2xl font-bold">{formatCurrency(350.25)}</p>
          <p className="text-xs text-gray-500 mt-1">Available in 7 days</p>
        </Card>
        
        <Card className="p-4">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Total Withdrawn</h3>
          <p className="text-2xl font-bold">{formatCurrency(2500.00)}</p>
        </Card>
      </div>
      
      <Card className="p-6">
        <form onSubmit={handleSubmit}>
          {error && (
            <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md">
              {error}
            </div>
          )}
          
          <div className="mb-6">
            <label htmlFor="withdrawAmount" className="block text-sm font-medium text-gray-700 mb-1">
              Withdrawal Amount
            </label>
            <div className="mt-1 relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="text"
                name="withdrawAmount"
                id="withdrawAmount"
                className="focus:ring-black focus:border-black block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
                placeholder="0.00"
                value={withdrawAmount}
                onChange={handleInputChange}
                required
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                <span className="text-gray-500 sm:text-sm">USD</span>
              </div>
            </div>
            <p className="mt-2 text-sm text-gray-500">
              Minimum withdrawal: $50.00
            </p>
          </div>
          
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Payment Method</h3>
            <div className="grid grid-cols-2 gap-4">
              <div
                className={`border rounded-md p-4 cursor-pointer ${
                  paymentMethod === 'paypal'
                    ? 'border-black bg-gray-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onClick={() => setPaymentMethod('paypal')}
              >
                <div className="flex items-center">
                  <input
                    type="radio"
                    name="paymentMethod"
                    id="paypal"
                    className="h-4 w-4 text-black focus:ring-black border-gray-300"
                    checked={paymentMethod === 'paypal'}
                    onChange={() => setPaymentMethod('paypal')}
                  />
                  <label htmlFor="paypal" className="ml-3 block text-sm font-medium text-gray-700">
                    PayPal
                  </label>
                </div>
              </div>
              
              <div
                className={`border rounded-md p-4 cursor-pointer ${
                  paymentMethod === 'bank'
                    ? 'border-black bg-gray-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onClick={() => setPaymentMethod('bank')}
              >
                <div className="flex items-center">
                  <input
                    type="radio"
                    name="paymentMethod"
                    id="bank"
                    className="h-4 w-4 text-black focus:ring-black border-gray-300"
                    checked={paymentMethod === 'bank'}
                    onChange={() => setPaymentMethod('bank')}
                  />
                  <label htmlFor="bank" className="ml-3 block text-sm font-medium text-gray-700">
                    Bank Transfer
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          {paymentMethod === 'paypal' && (
            <div className="mb-6">
              <Input
                label="PayPal Email"
                id="paypalEmail"
                name="paypalEmail"
                type="email"
                value={paymentDetails.paypalEmail}
                onChange={handleInputChange}
                required
                fullWidth
              />
            </div>
          )}
          
          {paymentMethod === 'bank' && (
            <div className="space-y-4 mb-6">
              <Input
                label="Bank Name"
                id="bankName"
                name="bankName"
                type="text"
                value={paymentDetails.bankName}
                onChange={handleInputChange}
                required
                fullWidth
              />
              
              <Input
                label="Account Number"
                id="accountNumber"
                name="accountNumber"
                type="text"
                value={paymentDetails.accountNumber}
                onChange={handleInputChange}
                required
                fullWidth
              />
              
              <Input
                label="Routing Number"
                id="routingNumber"
                name="routingNumber"
                type="text"
                value={paymentDetails.routingNumber}
                onChange={handleInputChange}
                required
                fullWidth
              />
              
              <Input
                label="Account Holder Name"
                id="accountHolderName"
                name="accountHolderName"
                type="text"
                value={paymentDetails.accountHolderName}
                onChange={handleInputChange}
                required
                fullWidth
              />
            </div>
          )}
          
          <div className="bg-gray-50 p-4 rounded-md mb-6">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Withdrawal Summary</h3>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Amount:</span>
                <span className="font-medium">
                  {withdrawAmount ? formatCurrency(parseFloat(withdrawAmount)) : '$0.00'}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Processing Fee:</span>
                <span className="font-medium">$0.00</span>
              </div>
              <div className="border-t border-gray-200 pt-2 mt-2">
                <div className="flex justify-between text-sm font-medium">
                  <span>Total:</span>
                  <span>
                    {withdrawAmount ? formatCurrency(parseFloat(withdrawAmount)) : '$0.00'}
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end">
            <Button
              type="submit"
              isLoading={submitting}
              disabled={!withdrawAmount || parseFloat(withdrawAmount) <= 0}
            >
              Withdraw Funds
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}
