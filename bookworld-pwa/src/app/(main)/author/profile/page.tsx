'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
// Removed framer-motion import for now
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import ProfilePicture from '@/components/profile/ProfilePicture';
import ProfilePictureSelector from '@/components/profile/ProfilePictureSelector';
import EditProfileForm from '@/components/profile/EditProfileForm';
import BookCard from '@/components/books/BookCard';
// import BookCarousel from '@/components/books/BookCarousel';
// Temporarily using simpler components
// import InkPenFollowerCounter from '@/components/author/InkPenFollowerCounter';
// import WorksCounter from '@/components/author/WorksCounter';
import AuthorSocialFeed from '@/components/social/AuthorSocialFeed';
import BookMailSubscribeModal from '@/components/author/BookMailSubscribeModal';
import type { BookData } from '@/components/books/BookCard';
import { useAuth } from '@/lib/hooks/useAuth';
import { getAuthorProfile, updateAuthorBio, followAuthor, subscribeToBookMail, type AuthorProfile } from '@/lib/utils/author';
import { getBooksByAuthor } from '@/lib/utils/books';

export default function AuthorProfilePage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isEditingProfilePic, setIsEditingProfilePic] = useState(false);
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isEditingBio, setIsEditingBio] = useState(false);
  const [isBookMailModalOpen, setIsBookMailModalOpen] = useState(false);
  const [currentProfilePic, setCurrentProfilePic] = useState<string | null>(null);
  const [currentBio, setCurrentBio] = useState('');
  const bioTextareaRef = useRef<HTMLTextAreaElement>(null);
  const [followerCount, setFollowerCount] = useState(0);
  const [isFollowerCountAnimating, setIsFollowerCountAnimating] = useState(false);
  const [activeTab, setActiveTab] = useState<'books' | 'feed'>('books');
  const [isLoading, setIsLoading] = useState(true);
  const [author, setAuthor] = useState<AuthorProfile | null>(null);
  const [publishedBooks, setPublishedBooks] = useState<BookData[]>([]);
  const [upcomingBooks, setUpcomingBooks] = useState<BookData[]>([]);

  // Maximum bio length
  const MAX_BIO_LENGTH = 250;

  // Get the author ID from the URL or use the current user's ID
  const authorId = user?.uid || 'a1'; // Fallback to a1 for development

  // Function to get social media icons
  const getSocialIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'twitter':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
          </svg>
        );
      case 'instagram':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
          </svg>
        );
      case 'facebook':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
          </svg>
        );
    }
  };

  // Load author data
  useEffect(() => {
    const loadAuthorData = async () => {
      setIsLoading(true);
      try {
        // Get author profile
        const authorProfile = await getAuthorProfile(authorId);
        if (authorProfile) {
          setAuthor(authorProfile);
          setCurrentBio(authorProfile.bio || '');
          setFollowerCount(authorProfile.followerCount || 0);

          // Get author's books
          const books = await getBooksByAuthor(authorId);

          // Split into published and upcoming books
          const published: BookData[] = [];
          const upcoming: BookData[] = [];

          for (const book of books) {
            if (book.isComingSoon) {
              upcoming.push(book);
            } else {
              published.push(book);
            }
          }

          setPublishedBooks(published);
          setUpcomingBooks(upcoming);
        }
      } catch (error) {
        console.error('Error loading author data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadAuthorData();
  }, [authorId]);

  // Focus the textarea when editing starts
  useEffect(() => {
    // Only run this effect when isEditingBio changes to true
    if (isEditingBio && bioTextareaRef.current) {
      // Focus the textarea
      const textarea = bioTextareaRef.current;
      textarea.focus();
    }
    // We're intentionally only running this when isEditingBio changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isEditingBio]);

  // Fallback mock data for development
  if (!author && !isLoading) {
    setAuthor({
      id: 'a1',
      name: 'Emily Chen',
      bio: currentBio || 'Award-winning fantasy author with a passion for world-building and character-driven narratives.',
      photoUrl: currentProfilePic || '/images/profile-placeholder.svg',
      website: 'https://emilychen-author.com',
      socialLinks: {
        twitter: 'https://twitter.com/emilychen',
        instagram: 'https://instagram.com/emilychen',
        facebook: 'https://facebook.com/emilychen'
      },
      followerCount: 1250,
      bookMailSubscriberCount: 450
    });
  }

  // All books combined
  const allBooks = [...publishedBooks, ...upcomingBooks];

  // Simulate follower count increase
  useEffect(() => {
    const interval = setInterval(() => {
      if (Math.random() > 0.7) {
        setIsFollowerCountAnimating(true);
        setFollowerCount(prev => prev + Math.floor(Math.random() * 10) + 1);
        setTimeout(() => setIsFollowerCountAnimating(false), 2000);
      }
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  // Handle profile picture save
  const handleSaveProfilePic = async (imageFile: File | string) => {
    try {
      // In a real implementation, you would upload the file to storage
      // and update the user's profile in the database

      // For demo purposes, we'll just set the local state
      if (typeof imageFile === 'string') {
        setCurrentProfilePic(imageFile);
      } else {
        // Create a URL for the file
        const url = URL.createObjectURL(imageFile);
        setCurrentProfilePic(url);
      }

      setIsEditingProfilePic(false);
    } catch (error) {
      console.error('Error saving profile picture:', error);
    }
  };

  // Handle profile save
  const handleSaveProfile = (updatedAuthor: AuthorProfile) => {
    if (!author) return;
    // In a real app, this would save to the backend
    console.log('Saving updated profile:', updatedAuthor);
    setAuthor(updatedAuthor);
    setIsEditingProfile(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Hero section with profile info */}
      <div className="relative bg-gradient-to-r from-purple-900 via-purple-800 to-indigo-900 text-gray-100 overflow-hidden">
        {/* Edit Profile Button */}
        <div className="absolute top-4 right-4 z-20">
          <button
            type="button"
            className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-full flex items-center space-x-2 backdrop-blur-sm transition-all"
            onClick={() => setIsEditingProfile(true)}
            disabled={!author}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
            <span>Edit Profile</span>
          </button>
        </div>
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%23ffffff\' fill-opacity=\'0.1\' fill-rule=\'evenodd\'/%3E%3C/svg%3E")',
            backgroundSize: '24px 24px'
          }} />
        </div>

        <div className="container mx-auto px-4 py-16 relative z-10">
          {isEditingProfile ? (
            <div className="max-w-4xl mx-auto bg-white text-gray-900 rounded-lg shadow-xl">
              {author && (
                <EditProfileForm
                  author={author}
                  userBooks={allBooks}
                  onSave={handleSaveProfile}
                  onCancel={() => setIsEditingProfile(false)}
                />
              )}
            </div>
          ) : isEditingProfilePic ? (
            <Card className="max-w-4xl mx-auto">
              <h2 className="text-xl font-bold mb-4 text-gray-900">Edit Profile Picture</h2>
              <ProfilePictureSelector
                currentImageUrl={currentProfilePic || undefined}
                userBooks={allBooks}
                onSave={handleSaveProfilePic}
                onCancel={() => setIsEditingProfilePic(false)}
              />
            </Card>
          ) : (
            <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
              {/* Profile picture and stats */}
              <div className="md:w-1/3 flex flex-col items-center">
                <div className="relative">
                  {author ? (
                    <ProfilePicture
                      src={author.photoUrl}
                      alt={author.name}
                      size="xl"
                      className="mb-4 ring-4 ring-white ring-opacity-50"
                    />
                  ) : (
                    <div className="w-32 h-32 rounded-full bg-gray-300 mb-4 ring-4 ring-white ring-opacity-50 flex items-center justify-center">
                      <span className="text-gray-500">Loading...</span>
                    </div>
                  )}
                  <button
                    type="button"
                    className="absolute -bottom-2 -right-2 bg-white rounded-full p-1 shadow-lg cursor-pointer hover:scale-110 transition-transform"
                    onClick={() => setIsEditingProfilePic(true)}
                    aria-label="Edit profile picture"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-600" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                  </button>
                </div>

                <h1 className="text-3xl font-bold mt-4 text-center">{author ? author.name : 'Loading...'}</h1>

                <div className="flex justify-center space-x-8 mt-6">
                  {/* Interactive follower counter */}
                  <button
                    type="button"
                    className="text-center group relative"
                    onClick={() => setFollowerCount(prev => prev + 1)}
                  >
                    <div className="text-4xl font-bold text-purple-600 group-hover:scale-110 transition-transform">{followerCount.toLocaleString()}</div>
                    <div className="text-sm text-white">Followers</div>
                    <div className="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-purple-600 text-white text-xs px-2 py-1 rounded-full">Follow +1</div>
                  </button>

                  {/* Works counter */}
                  <div className="text-center">
                    <div className="text-4xl font-bold text-purple-600">{allBooks.length}</div>
                    <div className="text-sm text-white">Works</div>
                  </div>
                </div>

                <div className="mt-6 flex flex-wrap justify-center gap-3">
                  {author && Object.entries(author.socialLinks).map(([platform, url]) => (
                    <a
                      key={platform}
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center w-10 h-10 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 hover:scale-110 transition-all"
                      aria-label={`Visit ${author.name}'s ${platform} profile`}
                    >
                      {getSocialIcon(platform)}
                      <span className="sr-only">{platform}</span>
                    </a>
                  ))}
                  {author && (
                    <a
                      href={author.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center w-10 h-10 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 hover:scale-110 transition-all"
                      aria-label={`Visit ${author.name}'s website`}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                      </svg>
                      <span className="sr-only">Website</span>
                    </a>
                  )}
                </div>
              </div>

              {/* Author bio and donate coffee button */}
              <div className="md:w-2/3">
                <div className="bg-gray-100 bg-opacity-10 backdrop-blur-sm rounded-xl p-6 mb-6 relative group shadow-sm">
                  {isEditingBio ? (
                    <div className="relative">
                      <textarea
                        ref={bioTextareaRef}
                        value={currentBio}
                        onChange={(e) => {
                          if (e.target.value.length <= MAX_BIO_LENGTH) {
                            setCurrentBio(e.target.value);
                          }
                        }}
                        className="w-full bg-gray-50 text-gray-800 border border-gray-200 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none text-lg leading-relaxed shadow-inner"
                        rows={5}
                        placeholder="Edit Me"
                        maxLength={MAX_BIO_LENGTH}
                      />
                      <div className="flex justify-between mt-2">
                        <span className="text-sm text-gray-200">
                          {currentBio.length}/{MAX_BIO_LENGTH} characters
                        </span>
                        <div className="space-x-2">
                          <button
                            type="button"
                            className="px-3 py-1 bg-gray-200 bg-opacity-90 hover:bg-gray-300 rounded-md text-sm text-gray-800 transition-colors shadow-sm"
                            onClick={() => setIsEditingBio(false)}
                          >
                            Cancel
                          </button>
                          <button
                            type="button"
                            className="px-3 py-1 bg-purple-600 hover:bg-purple-700 rounded-md text-sm text-gray-100 transition-colors shadow-sm"
                            onClick={async () => {
                              // Save the bio to Firestore
                              if (author?.id) {
                                try {
                                  const success = await updateAuthorBio(author.id, currentBio);
                                  if (success) {
                                    // Update the author object with the new bio
                                    setAuthor(prev => prev ? { ...prev, bio: currentBio } : null);
                                    setIsEditingBio(false);

                                    // Reload the author data to ensure we have the latest state
                                    const updatedProfile = await getAuthorProfile(author.id);
                                    if (updatedProfile) {
                                      setAuthor(updatedProfile);
                                      setCurrentBio(updatedProfile.bio || '');
                                    }
                                  } else {
                                    alert('Failed to save bio. Please try again.');
                                  }
                                } catch (error) {
                                  console.error('Error saving bio:', error);
                                  alert('Failed to save bio. Please try again.');
                                }
                              } else {
                                // Fallback for development
                                setIsEditingBio(false);
                                console.log('Bio saved (mock):', currentBio);
                              }
                            }}
                          >
                            Save
                          </button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <>
                      <button
                        type="button"
                        className="text-lg leading-relaxed cursor-pointer text-left w-full bg-transparent border-0 text-gray-100"
                        onClick={() => setIsEditingBio(true)}
                        aria-label="Click to edit bio"
                      >
                        {author ? author.bio : 'Loading...'}
                      </button>
                      <button
                        type="button"
                        className="absolute top-2 right-2 bg-white bg-opacity-0 group-hover:bg-opacity-20 p-1 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200"
                        onClick={() => setIsEditingBio(true)}
                        aria-label="Edit bio"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                        </svg>
                      </button>
                    </>
                  )}
                </div>

                <div className="flex flex-col space-y-4">
                  {/* BookMail Button */}
                  <button
                    type="button"
                    className="w-full py-3 px-6 bg-gradient-to-r from-purple-600 to-indigo-600 text-gray-100 font-bold rounded-full flex items-center justify-center hover:from-purple-700 hover:to-indigo-700 hover:scale-[1.02] active:scale-[0.98] transition-all shadow-md"
                    onClick={() => setIsBookMailModalOpen(true)}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                    Join BookMail
                  </button>

                  {/* Donate Coffee Button */}
                  <button
                    type="button"
                    className="w-full py-3 px-6 bg-gray-100 text-purple-700 font-bold rounded-full flex items-center justify-center hover:bg-gray-200 hover:scale-[1.02] active:scale-[0.98] transition-all shadow-sm"
                    onClick={() => alert('Donate Coffee feature')}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z" />
                    </svg>
                    Donate Coffee ($3.99)
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Content tabs */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex border-b border-gray-200 mb-8">
          <button
            type="button"
            className={`px-6 py-3 font-medium text-lg border-b-2 transition-colors ${activeTab === 'books' ? 'border-purple-600 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('books')}
          >
            Books
          </button>
          <button
            type="button"
            className={`px-6 py-3 font-medium text-lg border-b-2 transition-colors ${activeTab === 'feed' ? 'border-purple-600 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('feed')}
          >
            Updates
          </button>
        </div>

        {activeTab === 'books' ? (
            <div className="animate-fadeIn">
              {/* Audio Posts Section */}
              <div className="mb-12">
                <h2 className="text-2xl font-bold mb-6">Recent Audio Posts</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {[1, 2, 3, 4].map(i => (
                    <div key={i} className="bg-white rounded-lg shadow overflow-hidden">
                      <div className="p-4 border-b">
                        <div className="flex items-center">
                          <div className="relative w-10 h-10 rounded-full overflow-hidden mr-3">
                            <Image
                              src={'/images/profile-placeholder.svg'}
                              alt={author?.name || 'Author'}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div>
                            <h3 className="font-medium">{author?.name || 'Author'}</h3>
                            <p className="text-xs text-gray-500">{new Date(Date.now() - 86400000 * i).toLocaleDateString()}</p>
                          </div>
                        </div>
                      </div>
                      <div className="p-4">
                        <p className="text-gray-700 mb-4 line-clamp-2">Audio update about my writing progress on {i % 2 === 0 ? 'The Dragon\'s Legacy sequel' : 'Chronicles of the Forgotten'}...</p>
                        <button
                          type="button"
                          className="flex items-center px-3 py-2 rounded-full bg-gray-100 text-gray-700 hover:bg-gray-200"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                          </svg>
                          Play Audio
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Book display */}
              <div className="mb-12">
                <div className="bg-gradient-to-b from-gray-900 to-gray-800 rounded-lg p-8 text-white mb-8">
                  <h2 className="text-2xl font-bold mb-4">Featured Books</h2>
                  <p className="text-gray-300 mb-4">Explore {author ? author.name : 'Loading...'}'s collection of published and upcoming books.</p>
                </div>
              </div>

              {/* Published Books */}
              <div className="mb-12">
                <h2 className="text-2xl font-bold mb-6">Published Books</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {publishedBooks.map(book => (
                    <div key={book.id} className="relative">
                      <BookCard
                        book={book}
                        variant="grid"
                        showProfilePicButton={true}
                      />
                      {/* Book Link for published books */}
                      {!book.isComingSoon && (
                        <a
                          href={`/store/book/${book.id}`}
                          className="absolute bottom-0 left-0 right-0 bg-purple-600 text-white text-center py-2 hover:bg-purple-700 transition-colors"
                        >
                          Buy Now
                        </a>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Upcoming Books */}
              <div>
                <h2 className="text-2xl font-bold mb-6">Coming Soon</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {upcomingBooks.map(book => (
                    <div key={book.id} className="relative">
                      <BookCard
                        book={book}
                        variant="grid"
                        showProfilePicButton={true}
                      />
                      {/* Release Date for upcoming books */}
                      {book.releaseDate && (
                        <div className="absolute bottom-0 left-0 right-0 bg-gray-800 bg-opacity-80 text-white text-center py-2">
                          Release Date: {book.releaseDate.toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="animate-fadeIn">
              <AuthorSocialFeed authorId={author.id} />
            </div>
          )}
      </div>

      {/* BookMail Subscribe Modal */}
      {author && (
        <BookMailSubscribeModal
          authorName={author.name}
          authorPhotoUrl={author.photoUrl}
          isOpen={isBookMailModalOpen}
          onClose={() => setIsBookMailModalOpen(false)}
          onSubscribe={async ({ followAuthor: shouldFollow, subscribeToBookMail: shouldSubscribe }) => {
            let followSuccess = true;
            let subscribeSuccess = true;

            if (user && author) {
              // Follow the author if requested
              if (shouldFollow) {
                followSuccess = await followAuthor(user.uid, author.id);
                if (followSuccess) {
                  setFollowerCount(prev => prev + 1);
                }
              }

              // Subscribe to BookMail if requested
              if (shouldSubscribe) {
                subscribeSuccess = await subscribeToBookMail(user.uid, author.id);
              }

              setIsBookMailModalOpen(false);

              // Show appropriate success message
              if (followSuccess && subscribeSuccess) {
                if (shouldFollow && shouldSubscribe) {
                  alert(`You are now following ${author.name} and subscribed to their BookMail!`);
                } else if (shouldFollow) {
                  alert(`You are now following ${author.name}!`);
                } else if (shouldSubscribe) {
                  alert(`You are now subscribed to ${author.name}'s BookMail!`);
                }
              } else {
                // Show error message if something failed
                alert('There was an error processing your request. Please try again.');
              }
            } else {
              // Fallback for development or when user is not logged in
              setIsBookMailModalOpen(false);
              if (shouldFollow) {
                setFollowerCount(prev => prev + 1);
              }
              alert('Please log in to follow authors or subscribe to BookMail.');
            }
          }}
        />
      )}
    </div>
  );
}
