'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/lib/context/AuthContext';
import { getBooksByAuthor } from '@/lib/utils/books';
import { formatCurrency, formatNumber } from '@/lib/utils/format';
import BookCard from '@/components/books/BookCard';
import type { BookData } from '@/components/books/BookCard';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

export default function AuthorDashboardPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuthContext();

  const [books, setBooks] = useState<BookData[]>([]);
  const [earnings, setEarnings] = useState({
    total: 0,
    available: 0,
    pending: 0,
    lastMonth: 0,
    thisMonth: 0,
  });
  const [stats, setStats] = useState({
    totalSales: 0,
    totalReaders: 0,
    totalReviews: 0,
    averageRating: 0,
  });
  const [salesData, setSalesData] = useState([
    { month: 'Jan', sales: 12 },
    { month: 'Feb', sales: 19 },
    { month: 'Mar', sales: 15 },
    { month: 'Apr', sales: 25 },
    { month: 'May', sales: 32 },
    { month: 'Jun', sales: 28 },
    { month: 'Jul', sales: 35 },
    { month: 'Aug', sales: 42 },
    { month: 'Sep', sales: 38 },
    { month: 'Oct', sales: 45 },
    { month: 'Nov', sales: 50 },
    { month: 'Dec', sales: 65 },
  ]);
  const [recentSales, setRecentSales] = useState([
    { id: '1', bookTitle: 'The Silent Echo', buyer: 'John D.', amount: 9.99, date: new Date(Date.now() - 86400000 * 1) },
    { id: '2', bookTitle: 'Midnight Whispers', buyer: 'Sarah M.', amount: 12.99, date: new Date(Date.now() - 86400000 * 2) },
    { id: '3', bookTitle: 'The Silent Echo', buyer: 'Robert J.', amount: 9.99, date: new Date(Date.now() - 86400000 * 3) },
    { id: '4', bookTitle: 'Beyond the Horizon', buyer: 'Emily L.', amount: 14.99, date: new Date(Date.now() - 86400000 * 4) },
    { id: '5', bookTitle: 'Midnight Whispers', buyer: 'Michael P.', amount: 12.99, date: new Date(Date.now() - 86400000 * 5) },
  ]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchAuthorData = async () => {
      if (authLoading) return;

      if (!user) {
        router.push('/signin');
        return;
      }

      // Check if user is an author
      if (user.role !== 'author') {
        router.push('/');
        return;
      }

      try {
        setLoading(true);

        // Get author's books
        const authorBooks = await getBooksByAuthor(user.uid);
        setBooks(authorBooks);

        // Example data - in a real app, this would come from the backend
        setEarnings({
          total: 4250.75,
          available: 1250.50,
          pending: 350.25,
          lastMonth: 850.30,
          thisMonth: 1100.45,
        });

        setStats({
          totalSales: 425,
          totalReaders: 380,
          totalReviews: 128,
          averageRating: 4.7,
        });

        setLoading(false);
      } catch (error) {
        console.error('Error fetching author data:', error);
        setLoading(false);
      }
    };

    fetchAuthorData();
  }, [user, authLoading, router]);

  if (loading || authLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black" />
      </div>
    );
  }

  // Format date for display
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(date);
  };

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Author Dashboard</h1>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push('/author/profile')}
          >
            Author Profile
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push('/author/books')}
          >
            Manage Books
          </Button>
          <Button
            onClick={() => router.push('/author/books/new')}
          >
            Publish New Book
          </Button>
        </div>
      </div>

      {/* Earnings Overview */}
      <section className="mb-8">
        <h2 className="text-xl font-bold mb-4">Earnings Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="p-4">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Available for Withdrawal</h3>
            <p className="text-2xl font-bold">{formatCurrency(earnings.available)}</p>
            <div className="mt-4">
              <Button
                fullWidth
                onClick={() => router.push('/author/earnings/withdraw')}
              >
                Withdraw Funds
              </Button>
            </div>
          </Card>

          <Card className="p-4">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Pending Earnings</h3>
            <p className="text-2xl font-bold">{formatCurrency(earnings.pending)}</p>
            <p className="text-xs text-gray-500 mt-1">Available in 7 days</p>
          </Card>

          <Card className="p-4">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Total Earnings</h3>
            <p className="text-2xl font-bold">{formatCurrency(earnings.total)}</p>
            <div className="flex justify-between mt-2 text-xs">
              <div>
                <p className="text-gray-500">Last Month</p>
                <p className="font-medium">{formatCurrency(earnings.lastMonth)}</p>
              </div>
              <div>
                <p className="text-gray-500">This Month</p>
                <p className="font-medium">{formatCurrency(earnings.thisMonth)}</p>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* Sales Statistics */}
      <section className="mb-8">
        <h2 className="text-xl font-bold mb-4">Sales Statistics</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="p-4 text-center">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Total Sales</h3>
            <p className="text-2xl font-bold">{formatNumber(stats.totalSales)}</p>
          </Card>

          <Card className="p-4 text-center">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Total Readers</h3>
            <p className="text-2xl font-bold">{formatNumber(stats.totalReaders)}</p>
          </Card>

          <Card className="p-4 text-center">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Reviews</h3>
            <p className="text-2xl font-bold">{formatNumber(stats.totalReviews)}</p>
          </Card>

          <Card className="p-4 text-center">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Average Rating</h3>
            <p className="text-2xl font-bold">{stats.averageRating.toFixed(1)}</p>
            <div className="flex justify-center mt-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <svg
                  key={star}
                  xmlns="http://www.w3.org/2000/svg"
                  className={`h-4 w-4 ${star <= Math.round(stats.averageRating) ? 'text-yellow-500' : 'text-gray-300'}`}
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
          </Card>
        </div>
      </section>

      {/* Sales Chart */}
      <section className="mb-8">
        <Card className="p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Monthly Sales</h2>
            <select className="border rounded-md px-2 py-1 text-sm">
              <option>Last 12 months</option>
              <option>Last 6 months</option>
              <option>Last 3 months</option>
            </select>
          </div>

          <div className="h-64">
            <div className="flex h-full items-end">
              {salesData.map((month) => (
                <div key={month.month} className="flex-1 flex flex-col items-center">
                  <div
                    className="w-full bg-black rounded-t"
                    style={{ height: `${(month.sales / 65) * 100}%` }}
                  />
                  <div className="text-xs mt-2">{month.month}</div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </section>

      {/* Your Books */}
      <section className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Your Books</h2>
          <Button
            variant="ghost"
            onClick={() => router.push('/author/books')}
          >
            View All
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {books.slice(0, 3).map((book) => (
            <BookCard
              key={book.id}
              book={book}
              variant="grid"
            />
          ))}
        </div>
      </section>

      {/* Recent Sales */}
      <section>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Recent Sales</h2>
          <Button
            variant="ghost"
            onClick={() => router.push('/author/sales')}
          >
            View All
          </Button>
        </div>

        <Card>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Book
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Buyer
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentSales.map((sale) => (
                  <tr key={sale.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {sale.bookTitle}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {sale.buyer}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(sale.amount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(sale.date)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      </section>
    </div>
  );
}
