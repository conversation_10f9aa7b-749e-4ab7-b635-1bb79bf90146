'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/lib/context/AuthContext';
import { getBooksByAuthor } from '@/lib/utils/books';
import { formatCurrency } from '@/lib/utils/format';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { BookData } from '@/components/books/BookCard';

export default function AuthorBooksPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuthContext();
  
  const [books, setBooks] = useState<BookData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<'published' | 'drafts'>('published');
  const [searchQuery, setSearchQuery] = useState<string>('');
  
  useEffect(() => {
    const fetchAuthorBooks = async () => {
      if (authLoading) return;
      
      if (!user) {
        router.push('/signin');
        return;
      }
      
      // Check if user is an author
      if (user.role !== 'author') {
        router.push('/');
        return;
      }
      
      try {
        setLoading(true);
        
        // Get author's books
        const authorBooks = await getBooksByAuthor(user.uid, 100); // Get up to 100 books
        setBooks(authorBooks);
        
        setLoading(false);
      } catch (error) {
        console.error('Error fetching author books:', error);
        setLoading(false);
      }
    };
    
    fetchAuthorBooks();
  }, [user, authLoading, router]);
  
  if (loading || authLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black" />
      </div>
    );
  }
  
  // Filter books based on active tab
  const filteredBooks = books.filter((book) => {
    // In a real app, we would check the isPublished property
    // For now, we'll assume all books are published
    if (activeTab === 'published') {
      return true;
    } else {
      return false;
    }
  });
  
  // Filter books based on search query
  const searchedBooks = filteredBooks.filter((book) => {
    if (!searchQuery) return true;
    
    const query = searchQuery.toLowerCase();
    return (
      book.title.toLowerCase().includes(query) ||
      book.description.toLowerCase().includes(query)
    );
  });
  
  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">My Books</h1>
        <Button
          onClick={() => router.push('/author/books/new')}
        >
          Publish New Book
        </Button>
      </div>
      
      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          type="button"
          className={`pb-4 px-6 border-b-2 font-medium ${
            activeTab === 'published'
              ? 'border-black text-black'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
          onClick={() => setActiveTab('published')}
        >
          Published
        </button>
        <button
          type="button"
          className={`pb-4 px-6 border-b-2 font-medium ${
            activeTab === 'drafts'
              ? 'border-black text-black'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
          onClick={() => setActiveTab('drafts')}
        >
          Drafts
        </button>
      </div>
      
      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <input
            type="text"
            placeholder="Search your books..."
            className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      </div>
      
      {/* Book List */}
      {searchedBooks.length === 0 ? (
        <div className="text-center py-12">
          <h2 className="text-xl font-medium text-gray-900 mb-2">No books found</h2>
          <p className="text-gray-500 mb-6">
            {activeTab === 'published'
              ? "You haven't published any books yet."
              : "You don't have any drafts."}
          </p>
          <Button
            onClick={() => router.push('/author/books/new')}
          >
            Publish Your First Book
          </Button>
        </div>
      ) : (
        <div className="space-y-6">
          {searchedBooks.map((book) => (
            <Card key={book.id} className="overflow-hidden">
              <div className="flex flex-col md:flex-row">
                <div className="md:w-1/4 p-4">
                  <div className="relative aspect-[2/3] w-full max-w-[180px] mx-auto">
                    <img
                      src={book.coverUrl}
                      alt={book.title}
                      className="object-cover w-full h-full rounded-md"
                    />
                  </div>
                </div>
                
                <div className="md:w-3/4 p-4 border-t md:border-t-0 md:border-l border-gray-200">
                  <div className="flex flex-col h-full">
                    <div className="mb-4">
                      <h3 className="text-lg font-bold mb-1">{book.title}</h3>
                      <p className="text-sm text-gray-500">Published on {book.publishedAt.toLocaleDateString()}</p>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                      {book.description}
                    </p>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div>
                        <p className="text-xs text-gray-500">Price</p>
                        <p className="text-sm font-medium">{book.price === 0 ? 'Free' : formatCurrency(book.price)}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">Sales</p>
                        <p className="text-sm font-medium">0</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">Revenue</p>
                        <p className="text-sm font-medium">{formatCurrency(0)}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">Rating</p>
                        <p className="text-sm font-medium">
                          {book.rating ? `${book.rating.toFixed(1)} (${book.reviewCount})` : 'No ratings'}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center mt-auto">
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/author/books/edit/${book.id}`)}
                        >
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/author/books/analytics/${book.id}`)}
                        >
                          Analytics
                        </Button>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/books/${book.id}`)}
                      >
                        View Book
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
