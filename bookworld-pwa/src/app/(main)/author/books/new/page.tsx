'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/lib/context/AuthContext';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import { createBook, uploadBookCover, uploadBookContent, updateBookPricing } from '@/lib/utils/books';

export default function NewBookPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuthContext();

  const [loading, setLoading] = useState<boolean>(false);
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [bookData, setBookData] = useState({
    title: '',
    description: '',
    language: 'English',
    categories: [] as string[],
    tags: [] as string[],
    price: 0,
    isFree: false,
    coverFile: null as File | null,
    bookFile: null as File | null,
    previewFile: null as File | null,
    isbn: '',
    publishNow: true,
  });

  // Check if user is an author
  useEffect(() => {
    if (authLoading) return;

    if (!user) {
      router.push('/signin');
      return;
    }

    if (user.role !== 'author') {
      router.push('/');
      return;
    }
  }, [user, authLoading, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setBookData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setBookData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, fileType: 'coverFile' | 'bookFile' | 'previewFile') => {
    if (e.target.files?.[0]) {
      setBookData((prev) => ({ ...prev, [fileType]: e.target.files[0] }));
    }
  };

  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const tags = e.target.value.split(',').map((tag) => tag.trim());
    setBookData((prev) => ({ ...prev, tags }));
  };

  const handleCategoryChange = (category: string) => {
    setBookData((prev) => {
      const categories = prev.categories.includes(category)
        ? prev.categories.filter((c) => c !== category)
        : [...prev.categories, category];

      return { ...prev, categories };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);

      if (!user) {
        throw new Error('User not authenticated');
      }

      if (!bookData.coverFile || !bookData.bookFile) {
        throw new Error('Cover image and book file are required');
      }

      // 1. Create book document in Firestore
      const bookId = await createBook(user.uid, {
        title: bookData.title,
        description: bookData.description,
        language: bookData.language,
        categories: bookData.categories,
        tags: bookData.tags,
        isbn: bookData.isbn,
        isPublished: bookData.publishNow,
        coverUrl: '', // Will be updated after upload
      });

      // 2. Upload book cover
      await uploadBookCover(user.uid, bookId, bookData.coverFile);

      // 3. Upload book content
      await uploadBookContent(user.uid, bookId, bookData.bookFile);

      // 4. Upload preview file if provided
      if (bookData.previewFile) {
        // TODO: Implement preview file upload
      }

      // 5. Set book pricing
      await updateBookPricing(bookId, {
        price: bookData.isFree ? 0 : bookData.price,
        currency: 'USD',
      });

      // 6. Redirect to author dashboard
      router.push('/author/dashboard');
    } catch (error) {
      console.error('Error uploading book:', error);
      setLoading(false);
    }
  };

  const nextStep = () => {
    setCurrentStep((prev) => prev + 1);
  };

  const prevStep = () => {
    setCurrentStep((prev) => prev - 1);
  };

  if (authLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black" />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Publish New Book</h1>
      </div>

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {[1, 2, 3, 4].map((step) => (
            <div key={step} className="flex flex-col items-center">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  currentStep >= step
                    ? 'bg-black text-white'
                    : 'bg-gray-200 text-gray-500'
                }`}
              >
                {step}
              </div>
              <span className="text-xs mt-2">
                {step === 1
                  ? 'Book Details'
                  : step === 2
                  ? 'Upload Files'
                  : step === 3
                  ? 'Pricing'
                  : 'Review'}
              </span>
            </div>
          ))}
        </div>
        <div className="relative mt-2">
          <div className="absolute top-0 left-0 h-1 bg-gray-200 w-full" />
          <div
            className="absolute top-0 left-0 h-1 bg-black transition-all"
            style={{ width: `${((currentStep - 1) / 3) * 100}%` }}
          />
        </div>
      </div>

      <Card className="p-6">
        <form onSubmit={handleSubmit}>
          {/* Step 1: Book Details */}
          {currentStep === 1 && (
            <div>
              <h2 className="text-xl font-bold mb-6">Book Details</h2>

              <div className="space-y-4">
                <Input
                  label="Book Title"
                  id="title"
                  name="title"
                  value={bookData.title}
                  onChange={handleChange}
                  required
                  fullWidth
                />

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Book Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={5}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                    value={bookData.description}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div>
                  <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-1">
                    Language
                  </label>
                  <select
                    id="language"
                    name="language"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black"
                    value={bookData.language}
                    onChange={handleChange}
                  >
                    <option value="English">English</option>
                    <option value="Spanish">Spanish</option>
                    <option value="French">French</option>
                    <option value="German">German</option>
                    <option value="Chinese">Chinese</option>
                    <option value="Japanese">Japanese</option>
                    <option value="Other">Other</option>
                  </select>
                </div>

                <div>
                  <div className="block text-sm font-medium text-gray-700 mb-1">
                    Categories (select up to 3)
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {['Fiction', 'Non-Fiction', 'Fantasy', 'Sci-Fi', 'Romance', 'Mystery', 'Thriller', 'Horror', 'Biography', 'History', 'Self-Help', 'Business'].map((category) => (
                      <div key={category} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`category-${category}`}
                          checked={bookData.categories.includes(category)}
                          onChange={() => handleCategoryChange(category)}
                          disabled={bookData.categories.length >= 3 && !bookData.categories.includes(category)}
                          className="h-4 w-4 text-black focus:ring-black border-gray-300 rounded"
                        />
                        <label htmlFor={`category-${category}`} className="ml-2 text-sm text-gray-700">
                          {category}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <Input
                  label="Tags (comma separated)"
                  id="tags"
                  name="tags"
                  value={bookData.tags.join(', ')}
                  onChange={handleTagsChange}
                  placeholder="romance, fantasy, adventure"
                  fullWidth
                />

                <Input
                  label="ISBN (optional)"
                  id="isbn"
                  name="isbn"
                  value={bookData.isbn}
                  onChange={handleChange}
                  fullWidth
                />
              </div>

              <div className="mt-8 flex justify-end">
                <Button
                  type="button"
                  onClick={nextStep}
                >
                  Next: Upload Files
                </Button>
              </div>
            </div>
          )}

          {/* Step 2: Upload Files */}
          {currentStep === 2 && (
            <div>
              <h2 className="text-xl font-bold mb-6">Upload Files</h2>

              <div className="space-y-6">
                {/* Book Cover */}
                <div>
                  <label htmlFor="cover-upload" className="block text-sm font-medium text-gray-700 mb-2">
                    Book Cover Image (Required)
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center">
                    {bookData.coverFile ? (
                      <div className="text-center">
                        <div className="mb-3">
                          <img
                            src={URL.createObjectURL(bookData.coverFile)}
                            alt="Book cover preview"
                            className="h-40 object-contain mx-auto"
                          />
                        </div>
                        <p className="text-sm text-gray-500 mb-2">{bookData.coverFile.name}</p>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setBookData((prev) => ({ ...prev, coverFile: null }))}
                        >
                          Remove
                        </Button>
                      </div>
                    ) : (
                      <>
                        <svg
                          className="h-12 w-12 text-gray-400"
                          stroke="currentColor"
                          fill="none"
                          viewBox="0 0 48 48"
                          aria-hidden="true"
                        >
                          <path
                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                            strokeWidth={2}
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        <div className="flex text-sm text-gray-600 mt-4">
                          <label
                            htmlFor="cover-upload"
                            className="relative cursor-pointer bg-white rounded-md font-medium text-black hover:text-gray-700 focus-within:outline-none"
                          >
                            <span>Upload a file</span>
                            <input
                              id="cover-upload"
                              name="cover-upload"
                              type="file"
                              className="sr-only"
                              accept="image/*"
                              onChange={(e) => handleFileChange(e, 'coverFile')}
                              required
                            />
                          </label>
                          <p className="pl-1">or drag and drop</p>
                        </div>
                        <p className="text-xs text-gray-500 mt-2">
                          PNG, JPG, GIF up to 5MB. Recommended size: 1400 x 2100 pixels (2:3 ratio)
                        </p>
                      </>
                    )}
                  </div>
                </div>

                {/* Book File */}
                <div>
                  <label htmlFor="book-upload" className="block text-sm font-medium text-gray-700 mb-2">
                    Book File (Required)
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center">
                    {bookData.bookFile ? (
                      <div className="text-center">
                        <div className="mb-3">
                          <svg
                            className="h-12 w-12 text-gray-400 mx-auto"
                            stroke="currentColor"
                            fill="none"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                            />
                          </svg>
                        </div>
                        <p className="text-sm text-gray-500 mb-2">{bookData.bookFile.name}</p>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setBookData((prev) => ({ ...prev, bookFile: null }))}
                        >
                          Remove
                        </Button>
                      </div>
                    ) : (
                      <>
                        <svg
                          className="h-12 w-12 text-gray-400"
                          stroke="currentColor"
                          fill="none"
                          viewBox="0 0 24 24"
                          aria-hidden="true"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                          />
                        </svg>
                        <div className="flex text-sm text-gray-600 mt-4">
                          <label
                            htmlFor="book-upload"
                            className="relative cursor-pointer bg-white rounded-md font-medium text-black hover:text-gray-700 focus-within:outline-none"
                          >
                            <span>Upload a file</span>
                            <input
                              id="book-upload"
                              name="book-upload"
                              type="file"
                              className="sr-only"
                              accept=".epub,.pdf"
                              onChange={(e) => handleFileChange(e, 'bookFile')}
                              required
                            />
                          </label>
                          <p className="pl-1">or drag and drop</p>
                        </div>
                        <p className="text-xs text-gray-500 mt-2">
                          EPUB or PDF up to 100MB
                        </p>
                      </>
                    )}
                  </div>
                </div>

                {/* Preview File */}
                <div>
                  <label htmlFor="preview-upload" className="block text-sm font-medium text-gray-700 mb-2">
                    Preview File (Optional)
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center">
                    {bookData.previewFile ? (
                      <div className="text-center">
                        <div className="mb-3">
                          <svg
                            className="h-12 w-12 text-gray-400 mx-auto"
                            stroke="currentColor"
                            fill="none"
                            viewBox="0 0 24 24"
                            aria-hidden="true"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                            />
                          </svg>
                        </div>
                        <p className="text-sm text-gray-500 mb-2">{bookData.previewFile.name}</p>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setBookData((prev) => ({ ...prev, previewFile: null }))}
                        >
                          Remove
                        </Button>
                      </div>
                    ) : (
                      <>
                        <svg
                          className="h-12 w-12 text-gray-400"
                          stroke="currentColor"
                          fill="none"
                          viewBox="0 0 24 24"
                          aria-hidden="true"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                          />
                        </svg>
                        <div className="flex text-sm text-gray-600 mt-4">
                          <label
                            htmlFor="preview-upload"
                            className="relative cursor-pointer bg-white rounded-md font-medium text-black hover:text-gray-700 focus-within:outline-none"
                          >
                            <span>Upload a file</span>
                            <input
                              id="preview-upload"
                              name="preview-upload"
                              type="file"
                              className="sr-only"
                              accept=".epub,.pdf"
                              onChange={(e) => handleFileChange(e, 'previewFile')}
                            />
                          </label>
                          <p className="pl-1">or drag and drop</p>
                        </div>
                        <p className="text-xs text-gray-500 mt-2">
                          EPUB or PDF up to 10MB. This will be available as a free sample.
                        </p>
                      </>
                    )}
                  </div>
                </div>
              </div>

              <div className="mt-8 flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                >
                  Back
                </Button>
                <Button
                  type="button"
                  onClick={nextStep}
                >
                  Next: Pricing
                </Button>
              </div>
            </div>
          )}

          {/* Step 3: Pricing */}
          {currentStep === 3 && (
            <div>
              <h2 className="text-xl font-bold mb-6">Pricing</h2>

              <div className="space-y-6">
                <div className="flex items-center">
                  <input
                    id="isFree"
                    name="isFree"
                    type="checkbox"
                    className="h-4 w-4 text-black focus:ring-black border-gray-300 rounded"
                    checked={bookData.isFree}
                    onChange={handleCheckboxChange}
                  />
                  <label htmlFor="isFree" className="ml-2 block text-sm text-gray-900">
                    Make this book free
                  </label>
                </div>

                {!bookData.isFree && (
                  <div>
                    <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
                      Price (USD)
                    </label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">$</span>
                      </div>
                      <input
                        type="number"
                        name="price"
                        id="price"
                        className="focus:ring-black focus:border-black block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
                        placeholder="0.00"
                        step="0.01"
                        min="0.99"
                        max="99.99"
                        value={bookData.price}
                        onChange={handleChange}
                        required={!bookData.isFree}
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">USD</span>
                      </div>
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      Set a price between $0.99 and $99.99. You will receive 70% of the revenue.
                    </p>
                  </div>
                )}

                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="text-sm font-medium text-gray-900 mb-2">Pricing Summary</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Book Price:</span>
                      <span className="font-medium">{bookData.isFree ? 'Free' : `$${bookData.price.toFixed(2)}`}</span>
                    </div>
                    {!bookData.isFree && (
                      <>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Platform Fee (30%):</span>
                          <span className="font-medium">${(bookData.price * 0.3).toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Your Earnings (70%):</span>
                          <span className="font-medium">${(bookData.price * 0.7).toFixed(2)}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                <div>
                  <div className="flex items-center">
                    <input
                      id="publishNow"
                      name="publishNow"
                      type="checkbox"
                      className="h-4 w-4 text-black focus:ring-black border-gray-300 rounded"
                      checked={bookData.publishNow}
                      onChange={handleCheckboxChange}
                    />
                    <label htmlFor="publishNow" className="ml-2 block text-sm text-gray-900">
                      Publish immediately after submission
                    </label>
                  </div>
                  <p className="mt-2 text-sm text-gray-500 pl-6">
                    If unchecked, your book will be saved as a draft and you can publish it later.
                  </p>
                </div>
              </div>

              <div className="mt-8 flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                >
                  Back
                </Button>
                <Button
                  type="button"
                  onClick={nextStep}
                >
                  Next: Review
                </Button>
              </div>
            </div>
          )}

          {/* Step 4: Review */}
          {currentStep === 4 && (
            <div>
              <h2 className="text-xl font-bold mb-6">Review and Publish</h2>

              <div className="space-y-6">
                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="text-sm font-medium text-gray-900 mb-4">Book Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-xs text-gray-500">Title</p>
                      <p className="text-sm font-medium">{bookData.title}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Language</p>
                      <p className="text-sm font-medium">{bookData.language}</p>
                    </div>
                    <div className="md:col-span-2">
                      <p className="text-xs text-gray-500">Categories</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {bookData.categories.map((category) => (
                          <span key={category} className="text-xs bg-gray-200 px-2 py-1 rounded-full">
                            {category}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div className="md:col-span-2">
                      <p className="text-xs text-gray-500">Tags</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {bookData.tags.map((tag) => (
                          <span key={tag} className="text-xs bg-gray-200 px-2 py-1 rounded-full">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div className="md:col-span-2">
                      <p className="text-xs text-gray-500">Description</p>
                      <p className="text-sm">{bookData.description}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="text-sm font-medium text-gray-900 mb-4">Files</h3>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <svg
                        className="h-5 w-5 text-green-500 mr-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      <span className="text-sm">
                        Book Cover: {bookData.coverFile?.name}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <svg
                        className="h-5 w-5 text-green-500 mr-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      <span className="text-sm">
                        Book File: {bookData.bookFile?.name}
                      </span>
                    </div>
                    {bookData.previewFile && (
                      <div className="flex items-center">
                        <svg
                          className="h-5 w-5 text-green-500 mr-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        <span className="text-sm">
                          Preview File: {bookData.previewFile.name}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="text-sm font-medium text-gray-900 mb-4">Pricing</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Book Price:</span>
                      <span className="font-medium">{bookData.isFree ? 'Free' : `$${bookData.price.toFixed(2)}`}</span>
                    </div>
                    {!bookData.isFree && (
                      <>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Platform Fee (30%):</span>
                          <span className="font-medium">${(bookData.price * 0.3).toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Your Earnings (70%):</span>
                          <span className="font-medium">${(bookData.price * 0.7).toFixed(2)}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-md">
                  <h3 className="text-sm font-medium text-gray-900 mb-4">Publishing</h3>
                  <div className="flex items-center">
                    <svg
                      className={`h-5 w-5 ${bookData.publishNow ? 'text-green-500' : 'text-gray-400'} mr-2`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      aria-hidden="true"
                    >
                      {bookData.publishNow ? (
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      ) : (
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      )}
                    </svg>
                    <span className="text-sm">
                      {bookData.publishNow ? 'Publish immediately after submission' : 'Save as draft'}
                    </span>
                  </div>
                </div>

                <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-yellow-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-yellow-700">
                        By publishing this book, you confirm that you have the rights to distribute this content and agree to our Terms of Service.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-8 flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                >
                  Back
                </Button>
                <Button
                  type="submit"
                  isLoading={loading}
                >
                  {bookData.publishNow ? 'Publish Book' : 'Save as Draft'}
                </Button>
              </div>
            </div>
          )}

        </form>
      </Card>
    </div>
  );
}
