'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/lib/context/AuthContext';
import { getBookById } from '@/lib/utils/books';
import { formatCurrency, formatNumber } from '@/lib/utils/format';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { BookData } from '@/components/books/BookCard';

interface BookAnalyticsPageProps {
  params: {
    bookId: string;
  };
}

export default function BookAnalyticsPage({ params }: BookAnalyticsPageProps) {
  const { bookId } = params;
  const router = useRouter();
  const { user, loading: authLoading } = useAuthContext();
  
  const [book, setBook] = useState<BookData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  
  // Sample analytics data
  const [analytics, setAnalytics] = useState({
    sales: {
      total: 125,
      period: 18,
      change: 12.5,
    },
    revenue: {
      total: 1249.75,
      period: 179.82,
      change: 8.3,
    },
    readers: {
      total: 112,
      period: 15,
      change: 10.2,
    },
    completionRate: {
      value: 68,
      change: 5.4,
    },
    averageRating: {
      value: 4.7,
      count: 42,
      change: 0.2,
    },
    salesByCountry: [
      { country: 'United States', sales: 78, percentage: 62.4 },
      { country: 'United Kingdom', sales: 21, percentage: 16.8 },
      { country: 'Canada', sales: 12, percentage: 9.6 },
      { country: 'Australia', sales: 8, percentage: 6.4 },
      { country: 'Germany', sales: 6, percentage: 4.8 },
    ],
    salesByMonth: [
      { month: 'Jan', sales: 8 },
      { month: 'Feb', sales: 12 },
      { month: 'Mar', sales: 15 },
      { month: 'Apr', sales: 10 },
      { month: 'May', sales: 18 },
      { month: 'Jun', sales: 22 },
      { month: 'Jul', sales: 20 },
      { month: 'Aug', sales: 25 },
      { month: 'Sep', sales: 30 },
      { month: 'Oct', sales: 28 },
      { month: 'Nov', sales: 32 },
      { month: 'Dec', sales: 35 },
    ],
  });
  
  useEffect(() => {
    const fetchBookData = async () => {
      if (authLoading) return;
      
      if (!user) {
        router.push('/signin');
        return;
      }
      
      // Check if user is an author
      if (user.role !== 'author') {
        router.push('/');
        return;
      }
      
      try {
        setLoading(true);
        
        // Get book data
        const bookData = await getBookById(bookId);
        
        if (!bookData) {
          router.push('/author/books');
          return;
        }
        
        // Check if the book belongs to the current author
        if (bookData.author.id !== user.uid) {
          router.push('/author/books');
          return;
        }
        
        setBook(bookData as BookData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching book data:', error);
        setLoading(false);
      }
    };
    
    fetchBookData();
  }, [bookId, user, authLoading, router]);
  
  if (loading || authLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black" />
      </div>
    );
  }
  
  if (!book) {
    return null;
  }
  
  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.push('/author/books')}
          className="mb-4"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Books
        </Button>
        
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <h1 className="text-3xl font-bold mb-2 md:mb-0">{book.title}</h1>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => router.push(`/author/books/edit/${bookId}`)}
            >
              Edit Book
            </Button>
            <Button
              onClick={() => router.push(`/books/${bookId}`)}
            >
              View Book
            </Button>
          </div>
        </div>
      </div>
      
      {/* Time range selector */}
      <div className="flex justify-end mb-6">
        <div className="inline-flex rounded-md shadow-sm">
          <button
            type="button"
            className={`px-4 py-2 text-sm font-medium rounded-l-md ${
              timeRange === '7d'
                ? 'bg-black text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            } border border-gray-300`}
            onClick={() => setTimeRange('7d')}
          >
            7 days
          </button>
          <button
            type="button"
            className={`px-4 py-2 text-sm font-medium ${
              timeRange === '30d'
                ? 'bg-black text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            } border-t border-b border-gray-300`}
            onClick={() => setTimeRange('30d')}
          >
            30 days
          </button>
          <button
            type="button"
            className={`px-4 py-2 text-sm font-medium ${
              timeRange === '90d'
                ? 'bg-black text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            } border-t border-b border-gray-300`}
            onClick={() => setTimeRange('90d')}
          >
            90 days
          </button>
          <button
            type="button"
            className={`px-4 py-2 text-sm font-medium rounded-r-md ${
              timeRange === 'all'
                ? 'bg-black text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            } border border-gray-300`}
            onClick={() => setTimeRange('all')}
          >
            All time
          </button>
        </div>
      </div>
      
      {/* Key metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="p-4">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Total Sales</h3>
          <p className="text-2xl font-bold">{formatNumber(analytics.sales.total)}</p>
          <div className="flex items-center mt-2">
            <span className={`text-xs ${analytics.sales.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {analytics.sales.change >= 0 ? '+' : ''}{analytics.sales.change}%
            </span>
            <span className="text-xs text-gray-500 ml-1">vs. previous period</span>
          </div>
        </Card>
        
        <Card className="p-4">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Total Revenue</h3>
          <p className="text-2xl font-bold">{formatCurrency(analytics.revenue.total)}</p>
          <div className="flex items-center mt-2">
            <span className={`text-xs ${analytics.revenue.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {analytics.revenue.change >= 0 ? '+' : ''}{analytics.revenue.change}%
            </span>
            <span className="text-xs text-gray-500 ml-1">vs. previous period</span>
          </div>
        </Card>
        
        <Card className="p-4">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Total Readers</h3>
          <p className="text-2xl font-bold">{formatNumber(analytics.readers.total)}</p>
          <div className="flex items-center mt-2">
            <span className={`text-xs ${analytics.readers.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {analytics.readers.change >= 0 ? '+' : ''}{analytics.readers.change}%
            </span>
            <span className="text-xs text-gray-500 ml-1">vs. previous period</span>
          </div>
        </Card>
      </div>
      
      {/* Secondary metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card className="p-4">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Completion Rate</h3>
          <p className="text-2xl font-bold">{analytics.completionRate.value}%</p>
          <div className="flex items-center mt-2">
            <span className={`text-xs ${analytics.completionRate.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {analytics.completionRate.change >= 0 ? '+' : ''}{analytics.completionRate.change}%
            </span>
            <span className="text-xs text-gray-500 ml-1">vs. previous period</span>
          </div>
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-black rounded-full h-2"
                style={{ width: `${analytics.completionRate.value}%` }}
              />
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <h3 className="text-sm font-medium text-gray-500 mb-1">Average Rating</h3>
          <div className="flex items-center">
            <p className="text-2xl font-bold">{analytics.averageRating.value.toFixed(1)}</p>
            <div className="flex ml-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <svg
                  key={star}
                  xmlns="http://www.w3.org/2000/svg"
                  className={`h-5 w-5 ${star <= Math.round(analytics.averageRating.value) ? 'text-yellow-500' : 'text-gray-300'}`}
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
            <span className="text-sm text-gray-500 ml-2">({analytics.averageRating.count} reviews)</span>
          </div>
          <div className="flex items-center mt-2">
            <span className={`text-xs ${analytics.averageRating.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {analytics.averageRating.change >= 0 ? '+' : ''}{analytics.averageRating.change}
            </span>
            <span className="text-xs text-gray-500 ml-1">vs. previous period</span>
          </div>
        </Card>
      </div>
      
      {/* Sales chart */}
      <Card className="p-4 mb-8">
        <h3 className="text-lg font-medium mb-4">Sales Over Time</h3>
        <div className="h-64">
          <div className="flex h-full items-end">
            {analytics.salesByMonth.map((month) => (
              <div key={month.month} className="flex-1 flex flex-col items-center">
                <div 
                  className="w-full bg-black rounded-t"
                  style={{ height: `${(month.sales / 35) * 100}%` }}
                />
                <div className="text-xs mt-2">{month.month}</div>
              </div>
            ))}
          </div>
        </div>
      </Card>
      
      {/* Sales by country */}
      <Card className="p-4">
        <h3 className="text-lg font-medium mb-4">Sales by Country</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Country
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sales
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Percentage
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {analytics.salesByCountry.map((country) => (
                <tr key={country.country}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {country.country}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {country.sales}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {country.percentage}%
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
}
