'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/lib/context/AuthContext';
import { getBookById, hasUserPurchasedBook, getReadingProgress } from '@/lib/utils/books';
import EpubReader from '@/components/reader/EpubReader';

interface ReaderPageProps {
  params: {
    id: string;
  };
}

export default function ReaderPage({ params }: ReaderPageProps) {
  const { id } = params;
  const router = useRouter();
  const { user, loading: authLoading } = useAuthContext();
  
  const [bookContentUrl, setBookContentUrl] = useState<string | null>(null);
  const [initialLocation, setInitialLocation] = useState<string | null>(null);
  const [initialPage, setInitialPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchBookContent = async () => {
      if (authLoading) return;
      
      if (!user) {
        router.push('/signin');
        return;
      }
      
      try {
        setLoading(true);
        
        // Check if user has purchased the book
        const purchased = await hasUserPurchasedBook(user.uid, id);
        
        if (!purchased) {
          router.push(`/books/${id}`);
          return;
        }
        
        // Get book details
        const bookDetails = await getBookById(id);
        
        if (!bookDetails) {
          setError('Book not found');
          setLoading(false);
          return;
        }
        
        // Get content URL
        if (!bookDetails.contentUrl) {
          setError('Book content not available');
          setLoading(false);
          return;
        }
        
        setBookContentUrl(bookDetails.contentUrl);
        
        // Get reading progress
        const progress = await getReadingProgress(user.uid, id);
        
        if (progress) {
          setInitialPage(progress.currentPage);
          setTotalPages(progress.totalPages);
          // If there's a CFI location, use it
          if (progress.location) {
            setInitialLocation(progress.location);
          }
        }
        
        setLoading(false);
      } catch (error) {
        console.error('Error fetching book content:', error);
        setError('Failed to load book content');
        setLoading(false);
      }
    };
    
    fetchBookContent();
  }, [id, user, authLoading, router]);
  
  if (loading || authLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen px-4">
        <h1 className="text-2xl font-bold mb-4">Error</h1>
        <p className="text-gray-600 mb-6">{error}</p>
        <button
          className="px-4 py-2 bg-black text-white rounded-md"
          onClick={() => router.back()}
        >
          Go Back
        </button>
      </div>
    );
  }
  
  if (!bookContentUrl || !user) {
    return null;
  }
  
  return (
    <EpubReader
      bookId={id}
      userId={user.uid}
      contentUrl={bookContentUrl}
      initialLocation={initialLocation || undefined}
      initialPage={initialPage}
      totalPages={totalPages}
    />
  );
}
