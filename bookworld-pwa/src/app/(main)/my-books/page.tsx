'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/lib/context/AuthContext';
import { getUserLibrary, getReadingProgress } from '@/lib/utils/books';
import BookCard, { BookData } from '@/components/books/BookCard';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

export default function MyBooksPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuthContext();
  
  const [books, setBooks] = useState<BookData[]>([]);
  const [readingProgress, setReadingProgress] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<'all' | 'reading' | 'completed'>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortBy, setSortBy] = useState<'recent' | 'title' | 'author' | 'progress'>('recent');
  
  useEffect(() => {
    const fetchLibrary = async () => {
      if (authLoading) return;
      
      if (!user) {
        router.push('/signin');
        return;
      }
      
      try {
        setLoading(true);
        
        // Get user's library
        const libraryBooks = await getUserLibrary(user.uid);
        setBooks(libraryBooks);
        
        // Get reading progress for each book
        const progressData: Record<string, number> = {};
        
        for (const book of libraryBooks) {
          const progress = await getReadingProgress(user.uid, book.id);
          if (progress) {
            progressData[book.id] = progress.progress;
          } else {
            progressData[book.id] = 0;
          }
        }
        
        setReadingProgress(progressData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching library:', error);
        setLoading(false);
      }
    };
    
    fetchLibrary();
  }, [user, authLoading, router]);
  
  if (loading || authLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black" />
      </div>
    );
  }
  
  // Filter books based on active tab
  const filteredBooks = books.filter((book) => {
    const progress = readingProgress[book.id] || 0;
    
    if (activeTab === 'reading') {
      return progress > 0 && progress < 100;
    } else if (activeTab === 'completed') {
      return progress === 100;
    }
    
    return true;
  });
  
  // Filter books based on search query
  const searchedBooks = filteredBooks.filter((book) => {
    if (!searchQuery) return true;
    
    const query = searchQuery.toLowerCase();
    return (
      book.title.toLowerCase().includes(query) ||
      book.author.name.toLowerCase().includes(query)
    );
  });
  
  // Sort books
  const sortedBooks = [...searchedBooks].sort((a, b) => {
    if (sortBy === 'title') {
      return a.title.localeCompare(b.title);
    } else if (sortBy === 'author') {
      return a.author.name.localeCompare(b.author.name);
    } else if (sortBy === 'progress') {
      return (readingProgress[b.id] || 0) - (readingProgress[a.id] || 0);
    } else {
      // Default: sort by recent (purchase date)
      return b.publishedAt.getTime() - a.publishedAt.getTime();
    }
  });
  
  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">My Books</h1>
        <Button
          onClick={() => router.push('/marketplace')}
        >
          Browse Marketplace
        </Button>
      </div>
      
      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          type="button"
          className={`pb-4 px-6 border-b-2 font-medium ${
            activeTab === 'all'
              ? 'border-black text-black'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
          onClick={() => setActiveTab('all')}
        >
          All Books
        </button>
        <button
          type="button"
          className={`pb-4 px-6 border-b-2 font-medium ${
            activeTab === 'reading'
              ? 'border-black text-black'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
          onClick={() => setActiveTab('reading')}
        >
          Currently Reading
        </button>
        <button
          type="button"
          className={`pb-4 px-6 border-b-2 font-medium ${
            activeTab === 'completed'
              ? 'border-black text-black'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
          onClick={() => setActiveTab('completed')}
        >
          Completed
        </button>
      </div>
      
      {/* Search and Sort */}
      <div className="flex flex-col md:flex-row justify-between mb-6">
        <div className="relative mb-4 md:mb-0 md:w-1/3">
          <input
            type="text"
            placeholder="Search your books..."
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        
        <div className="flex items-center">
          <span className="text-sm text-gray-500 mr-2">Sort by:</span>
          <select
            className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
          >
            <option value="recent">Recently Added</option>
            <option value="title">Title</option>
            <option value="author">Author</option>
            <option value="progress">Reading Progress</option>
          </select>
        </div>
      </div>
      
      {/* Book List */}
      {sortedBooks.length === 0 ? (
        <div className="text-center py-12">
          <h2 className="text-xl font-medium text-gray-900 mb-2">No books found</h2>
          <p className="text-gray-500 mb-6">
            {activeTab === 'all'
              ? "You haven't purchased any books yet."
              : activeTab === 'reading'
              ? "You don't have any books in progress."
              : "You haven't completed any books yet."}
          </p>
          <Button
            onClick={() => router.push('/marketplace')}
          >
            Browse Marketplace
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6">
          {sortedBooks.map((book) => (
            <Card key={book.id} className="overflow-hidden">
              <div className="flex flex-col md:flex-row">
                <div className="md:w-1/4 p-4">
                  <BookCard
                    book={book}
                    variant="compact"
                    showReadButton
                    isPurchased
                  />
                </div>
                
                <div className="md:w-3/4 p-4 border-t md:border-t-0 md:border-l border-gray-200">
                  <div className="flex flex-col h-full">
                    <div className="mb-4">
                      <h3 className="text-lg font-bold mb-1">{book.title}</h3>
                      <p className="text-sm text-gray-500">by {book.author.name}</p>
                    </div>
                    
                    <div className="mb-4 flex-1">
                      <div className="flex justify-between mb-1">
                        <span className="text-sm text-gray-500">Reading Progress</span>
                        <span className="text-sm font-medium">{readingProgress[book.id] || 0}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-black rounded-full h-2"
                          style={{ width: `${readingProgress[book.id] || 0}%` }}
                        />
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div className="text-sm text-gray-500">
                        {readingProgress[book.id] === 0
                          ? 'Not started yet'
                          : readingProgress[book.id] === 100
                          ? 'Completed'
                          : `${readingProgress[book.id]}% completed`}
                      </div>
                      <Button
                        onClick={() => router.push(`/reader/${book.id}`)}
                      >
                        {readingProgress[book.id] === 0
                          ? 'Start Reading'
                          : readingProgress[book.id] === 100
                          ? 'Read Again'
                          : 'Continue Reading'}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
