import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@/lib/supabase/server';
import sharp from 'sharp';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;
    
    // Get form data
    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    const bookCoverUrl = formData.get('bookCoverUrl') as string | null;
    
    // Initialize Supabase client
    const supabase = createClient();
    
    // Handle book cover URL case
    if (bookCoverUrl) {
      // Update user profile with the book cover URL
      const { error } = await supabase
        .from('user_profiles')
        .update({ profile_picture_url: bookCoverUrl })
        .eq('user_id', userId);
      
      if (error) {
        console.error('Error updating profile with book cover:', error);
        return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 });
      }
      
      return NextResponse.json({ 
        success: true, 
        profilePictureUrl: bookCoverUrl 
      });
    }
    
    // Handle file upload case
    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }
    
    // Check file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ error: 'File must be an image' }, { status: 400 });
    }
    
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json({ error: 'File size must be less than 5MB' }, { status: 400 });
    }
    
    // Get crop settings
    const xOffset = parseFloat(formData.get('xOffset') as string || '0');
    const yOffset = parseFloat(formData.get('yOffset') as string || '0');
    const scale = parseFloat(formData.get('scale') as string || '1');
    
    // Process the image with sharp
    const buffer = Buffer.from(await file.arrayBuffer());
    
    // Get image dimensions
    const metadata = await sharp(buffer).metadata();
    const { width = 0, height = 0 } = metadata;
    
    // Calculate crop dimensions to achieve 10:16 aspect ratio
    const targetRatio = 10 / 16;
    let cropWidth, cropHeight, cropLeft, cropTop;
    
    if (width / height > targetRatio) {
      // Image is wider than target ratio
      cropHeight = height;
      cropWidth = height * targetRatio;
      cropLeft = Math.round((width - cropWidth) / 2 + (width * xOffset / 100));
      cropTop = Math.round(height * yOffset / 100);
    } else {
      // Image is taller than target ratio
      cropWidth = width;
      cropHeight = width / targetRatio;
      cropLeft = Math.round(width * xOffset / 100);
      cropTop = Math.round((height - cropHeight) / 2 + (height * yOffset / 100));
    }
    
    // Apply scale factor
    if (scale !== 1) {
      const scaledWidth = cropWidth / scale;
      const scaledHeight = cropHeight / scale;
      cropLeft += (cropWidth - scaledWidth) / 2;
      cropTop += (cropHeight - scaledHeight) / 2;
      cropWidth = scaledWidth;
      cropHeight = scaledHeight;
    }
    
    // Ensure crop dimensions are valid
    cropLeft = Math.max(0, cropLeft);
    cropTop = Math.max(0, cropTop);
    cropWidth = Math.min(width - cropLeft, cropWidth);
    cropHeight = Math.min(height - cropTop, cropHeight);
    
    // Process the image
    const processedImageBuffer = await sharp(buffer)
      .extract({
        left: Math.round(cropLeft),
        top: Math.round(cropTop),
        width: Math.round(cropWidth),
        height: Math.round(cropHeight)
      })
      .resize({
        width: 400,
        height: 640,
        fit: 'cover'
      })
      .jpeg({ quality: 85 })
      .toBuffer();
    
    // Upload to Supabase Storage
    const fileName = `profile-${userId}-${Date.now()}.jpg`;
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('profile-pictures')
      .upload(fileName, processedImageBuffer, {
        contentType: 'image/jpeg',
        upsert: true
      });
    
    if (uploadError) {
      console.error('Error uploading to storage:', uploadError);
      return NextResponse.json({ error: 'Failed to upload image' }, { status: 500 });
    }
    
    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('profile-pictures')
      .getPublicUrl(fileName);
    
    // Update user profile with the new image URL
    const { error: updateError } = await supabase
      .from('user_profiles')
      .update({ profile_picture_url: publicUrl })
      .eq('user_id', userId);
    
    if (updateError) {
      console.error('Error updating profile:', updateError);
      return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 });
    }
    
    return NextResponse.json({ 
      success: true, 
      profilePictureUrl: publicUrl 
    });
    
  } catch (error) {
    console.error('Error processing profile picture:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
