'use client';

import React, { useEffect } from 'react';
import type { ReactNode } from 'react';
import { AuthProvider } from '@/lib/context/AuthContext';

interface ClientLayoutProps {
  children: ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  // Handle hydration errors from browser extensions
  useEffect(() => {
    // Use a short timeout to ensure this runs after hydration
    const timer = setTimeout(() => {
      // Remove specific extension classes but preserve other classes
      if (document.documentElement.classList.contains('js-storylane-extension')) {
        document.documentElement.classList.remove('js-storylane-extension');
      }
    }, 0);

    return () => clearTimeout(timer);
  }, []);

  return <AuthProvider>{children}</AuthProvider>;
}
