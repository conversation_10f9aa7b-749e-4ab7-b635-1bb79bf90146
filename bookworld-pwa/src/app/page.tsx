'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/lib/context/AuthContext';
import dynamic from 'next/dynamic';
import Portal from '@/components/ui/Portal';

// Dynamically import the BookGlobe component with no SSR
const BookGlobe = dynamic(() => import('@/components/globe/BookGlobe'), {
  ssr: false,
  loading: () => (
    <div className="w-64 h-64 rounded-full bg-gray-200 animate-pulse flex items-center justify-center">
      <span className="text-gray-400">Loading...</span>
    </div>
  ),
});

// Dynamically import the interactive effects
const BookParticles = dynamic(() => import('@/components/effects/InteractiveEffects').then(mod => mod.BookParticles), { ssr: false });
const MouseTrailEffect = dynamic(() => import('@/components/effects/InteractiveEffects').then(mod => mod.MouseTrailEffect), { ssr: false });

export default function Home() {
  const router = useRouter();
  const { user, loading } = useAuthContext();
  const [showAnimation, setShowAnimation] = useState(true);
  const [scrollY, setScrollY] = useState(0);

  // Check if user is already logged in
  useEffect(() => {
    if (!loading && user) {
      router.push('/home');
    }

    // Hide loading animation after 1.5 seconds
    const timer = setTimeout(() => {
      setShowAnimation(false);
    }, 1500);

    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [user, loading, router]);

  // If still loading or animation is showing, display loading spinner
  if (loading || showAnimation) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-black">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white" />
      </div>
    );
  }

  // Calculate opacity based on scroll position
  const heroOpacity = Math.max(0, 1 - scrollY / 500);

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Interactive effects */}
      <BookParticles />
      <MouseTrailEffect />

      {/* Hero Section with Globe */}
      <div className="relative min-h-screen flex flex-col justify-center items-center text-center px-4">
        {/* 3D Globe */}
        <div
          className="absolute inset-0 flex justify-center items-center pointer-events-none z-0"
          style={{ opacity: heroOpacity }}
        >
          <div className="w-full h-full max-w-[800px] max-h-[800px] mx-auto">
            <BookGlobe />
          </div>
        </div>

        {/* Content that overlays the globe */}
        <div className="relative z-10 mt-16 mb-12">
          <div className="mb-6">
            <h1 className="text-6xl md:text-8xl font-bold mb-2 tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-white via-white to-gray-300">BOOKWORLD</h1>
            <div className="w-24 h-0.5 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mb-4" />
          </div>
          <p className="text-lg md:text-xl text-gray-300 max-w-xl mx-auto font-light tracking-wide uppercase">
            Read · Create · Connect · Earn
          </p>
        </div>

        {/* Portal Entry Points */}
        <div className="flex justify-center items-center gap-32 mt-12 relative z-10">
          {/* Book Cover Creator Portal */}
          <Portal
            title={<>
              <div className="text-2xl">BOOK</div>
              <div className="text-2xl">COVER</div>
              <div className="text-2xl">CREATOR</div>
            </>}
            description=""
            color="purple"
            icon={null}
            onClick={() => router.push('/cover-creator')}
          />

          {/* Enter BookWorld Portal */}
          <Portal
            title={<>
              <div className="text-2xl">ENTER</div>
              <div className="text-2xl">BOOK</div>
              <div className="text-2xl">WORLD</div>
            </>}
            description=""
            color="rose"
            icon={null}
            onClick={() => router.push('/signin')}
          />
        </div>

        {/* Features Section with fade-in effect */}
        <div className="mt-24 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl w-full relative z-10">
          <div className="text-center p-6 transform transition-all duration-700" style={{ opacity: scrollY > 200 ? 1 : 0, transform: `translateY(${scrollY > 200 ? 0 : 30}px)` }}>
            <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center rounded-full bg-amber-500/20 text-amber-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2">Audio Posts</h3>
            <p className="text-gray-400">Share your thoughts with 30-second audio clips about your favorite books</p>
          </div>

          <div className="text-center p-6 transform transition-all duration-700" style={{ opacity: scrollY > 250 ? 1 : 0, transform: `translateY(${scrollY > 250 ? 0 : 30}px)` }}>
            <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center rounded-full bg-indigo-500/20 text-indigo-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2">Social Reading</h3>
            <p className="text-gray-400">Connect with other readers, share highlights, and discuss your favorite books</p>
          </div>

          <div className="text-center p-6 transform transition-all duration-700" style={{ opacity: scrollY > 300 ? 1 : 0, transform: `translateY(${scrollY > 300 ? 0 : 30}px)` }}>
            <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center rounded-full bg-rose-500/20 text-rose-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
              </svg>
            </div>
            <h3 className="text-xl font-bold mb-2">Book Store</h3>
            <p className="text-gray-400">Discover and purchase books directly from authors, or sell your own and earn 70%</p>
          </div>
        </div>

        {/* About BookWorld Section */}
        <div className="mt-32 max-w-6xl w-full relative z-10 px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6 inline-block bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-pink-500">Discover BookWorld</h2>
            <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto" />
            <p className="text-gray-300 mt-6 max-w-3xl mx-auto">BookWorld is revolutionizing how we experience books by combining reading, social networking, and publishing in one seamless platform.</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-900/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-800 transform transition-all hover:shadow-[0_0_30px_rgba(168,85,247,0.2)] hover:border-purple-800">
              <h3 className="text-2xl font-bold mb-4 text-purple-300">For Readers</h3>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" role="img">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300">Discover new books through a community of passionate readers</p>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" role="img">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300">Share your reading journey with audio posts and highlights</p>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" role="img">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300">Enjoy an integrated e-reader with social features</p>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" role="img">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300">Track reading progress and set personal reading goals</p>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" role="img">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300"><span className="text-purple-300 font-medium">Earn rewards</span> by engaging with content and participating in the community</p>
                </li>
              </ul>
            </div>

            <div className="bg-gray-900/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-800 transform transition-all hover:shadow-[0_0_30px_rgba(244,63,94,0.2)] hover:border-rose-800">
              <h3 className="text-2xl font-bold mb-4 text-rose-300">For Authors</h3>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-rose-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-rose-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" role="img">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300">Publish directly to a community of engaged readers</p>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-rose-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-rose-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" role="img">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300">Create stunning book covers with professional design tools</p>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-rose-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-rose-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" role="img">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300">Engage directly with readers through events and discussions</p>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-rose-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-rose-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" role="img">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300">Earn 70% revenue share with transparent analytics</p>
                </li>
              </ul>
            </div>

            <div className="bg-gray-900/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-800 transform transition-all hover:shadow-[0_0_30px_rgba(59,130,246,0.2)] hover:border-blue-800">
              <h3 className="text-2xl font-bold mb-4 text-blue-300">Social Features</h3>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" role="img">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300">Timeline with audio posts and book cover visuals</p>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" role="img">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300">Join book clubs and reading groups with shared interests</p>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" role="img">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300">Live reading sessions and author Q&A events</p>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center mr-3 mt-0.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" role="img">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-300">Share book highlights and annotations with followers</p>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(ellipse_at_center,rgba(168,85,247,0.15),transparent_70%)]" />
          <div className="absolute top-1/4 -left-20 w-60 h-60 rounded-full bg-purple-600/10 blur-3xl" />
          <div className="absolute bottom-1/3 -right-20 w-80 h-80 rounded-full bg-rose-600/10 blur-3xl" />
        </div>
      </div>
      {/* Footer */}
      <footer className="py-8 px-4 border-t border-gray-800">
        <div className="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white text-xs font-bold mr-2">BW</div>
              <span className="font-bold text-lg text-white">BookWorld</span>
            </div>
          </div>

          <div className="text-sm text-gray-500">
            &copy; {new Date().getFullYear()} BookWorld. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}
