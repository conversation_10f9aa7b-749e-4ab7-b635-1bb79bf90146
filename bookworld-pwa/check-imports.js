// Simple script to check if modules can be imported correctly
console.log('Checking module imports...');

try {
  console.log('Importing useAuth...');
  const useAuth = require('./src/lib/hooks/useAuth');
  console.log('useAuth imported successfully:', useAuth);
} catch (error) {
  console.error('Error importing useAuth:', error);
}

try {
  console.log('Importing author utils...');
  const authorUtils = require('./src/lib/utils/author');
  console.log('author utils imported successfully:', authorUtils);
} catch (error) {
  console.error('Error importing author utils:', error);
}

try {
  console.log('Importing books utils...');
  const booksUtils = require('./src/lib/utils/books');
  console.log('books utils imported successfully:', booksUtils);
} catch (error) {
  console.error('Error importing books utils:', error);
}

console.log('Module import check complete.'); 