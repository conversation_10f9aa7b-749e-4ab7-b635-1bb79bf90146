/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    // Fix for Firebase/undici compatibility issue
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        util: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      };
    }

    // Handle undici and other problematic modules
    config.module.rules.push({
      test: /\.m?js$/,
      include: /node_modules\/(undici|@firebase)/,
      type: 'javascript/auto',
      resolve: {
        fullySpecified: false,
      },
    });

    // Ignore specific problematic modules
    config.externals = config.externals || [];
    if (!isServer) {
      config.externals.push({
        'undici': 'undici',
      });
    }

    return config;
  },
  experimental: {
    esmExternals: 'loose',
  },
}

module.exports = nextConfig
