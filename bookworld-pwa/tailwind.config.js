/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'float-slow': 'float-slow 8s ease-in-out infinite',
        'float-medium': 'float-medium 7s ease-in-out infinite',
        'float-fast': 'float-fast 4s ease-in-out infinite',
        'float-slower': 'float-slower 10s ease-in-out infinite',
        'pulse-slow': 'pulse 3s ease-in-out infinite',
        'portal-swirl': 'portalSwirl 8s linear infinite',
      },
      keyframes: {
        float: {
          '0%': { transform: 'translateY(0px) rotate(0deg)' },
          '50%': { transform: 'translateY(-10px) rotate(2deg)' },
          '100%': { transform: 'translateY(0px) rotate(0deg)' },
        },
        'float-slow': {
          '0%': { transform: 'translateY(0px) rotate(0deg)' },
          '50%': { transform: 'translateY(-15px) rotate(-3deg)' },
          '100%': { transform: 'translateY(0px) rotate(0deg)' },
        },
        'float-slower': {
          '0%': { transform: 'translateY(0px) rotate(0deg)' },
          '50%': { transform: 'translateY(-8px) rotate(1deg)' },
          '100%': { transform: 'translateY(0px) rotate(0deg)' },
        },
        'float-medium': {
          '0%': { transform: 'translateY(0px) rotate(0deg)' },
          '50%': { transform: 'translateY(-12px) rotate(4deg)' },
          '100%': { transform: 'translateY(0px) rotate(0deg)' },
        },
        'float-fast': {
          '0%': { transform: 'translateY(0px) rotate(0deg)' },
          '50%': { transform: 'translateY(-6px) rotate(1deg)' },
          '100%': { transform: 'translateY(0px) rotate(0deg)' },
        },
        portalSwirl: {
          '0%': { transform: 'rotate(0deg) scale(2.5)' },
          '100%': { transform: 'rotate(360deg) scale(2.5)' },
        },
      },
      perspective: {
        'default': '1000px',
      },
      rotate: {
        'y-3': 'rotateY(3deg)',
      },
      transformOrigin: {
        'center': 'center',
      },
      fontFamily: {
        sans: ['var(--font-inter)'],
      },
    },
  },
  plugins: [],
};
