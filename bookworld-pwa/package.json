{"name": "bookworld-pwa", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/material": "^5.13.0", "@types/three": "^0.175.0", "firebase": "^10.0.0", "next": "14.0.0", "next-pwa": "^5.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-firebase-hooks": "^5.1.1", "sharp": "^0.32.0", "three": "^0.175.0", "undici": "^5.28.3"}, "devDependencies": {"@babel/core": "^7.26.10", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.21", "babel-loader": "^10.0.0", "eslint": "^8.0.0", "eslint-config-next": "14.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.0.0"}}