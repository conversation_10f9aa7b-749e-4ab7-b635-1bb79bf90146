rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow authenticated users to read and write their own profile pictures
    match /users/{userId}/{fileName} {
      allow read: if true;  // Anyone can read profile pictures
      allow write: if request.auth != null 
        && request.auth.uid == userId
        && request.resource.size < 5 * 1024 * 1024  // 5MB max file size
        && request.resource.contentType.matches('image/.*');  // Only allow images
    }
    
    // Default rule - deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
